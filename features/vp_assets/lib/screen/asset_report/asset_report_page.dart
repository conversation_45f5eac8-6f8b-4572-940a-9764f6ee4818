import 'package:flutter/material.dart';
import 'package:vp_assets/cubit/assets/asset_summary_cubit.dart';
import 'package:vp_assets/generated/l10n.dart';
import 'package:vp_assets/model/asset_overview/assset_summary_model.dart';
import 'package:vp_assets/model/asset_report/chart_data_report_model.dart';
import 'package:vp_assets/screen/asset_report/widget/asset_allocation_widget.dart';
import 'package:vp_assets/screen/asset_report/widget/asset_growth_widget.dart';
import 'package:vp_assets/screen/asset_report/widget/cash_and_holding_widget.dart';
import 'package:vp_assets/screen/asset_report/widget/net_assets_and_liabilities.dart';
import 'package:vp_assets/widgets/selected_sub_account_widget.dart';
import 'package:vp_common/vp_common.dart' show Assets, DateTimeExts;
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';

class AssetReportPage extends StatefulWidget {
  const AssetReportPage({Key? key}) : super(key: key);

  @override
  State<AssetReportPage> createState() => _AssetReportPageState();
}

class _AssetReportPageState extends State<AssetReportPage>
    with AutomaticKeepAliveClientMixin {
  @override
  void dispose() {
    super.dispose();
  }

  // void onSubAccountChanged(SubAccountModel acc) {
  //   _assetReportCubit.onSubAccountChanged(acc);

  //   setState(() {});
  // }
  Widget buildContentView() {
    return BlocBuilder<AssetSummaryCubit, AssetSummaryState>(
      builder: (contextSum, state) {
        final showAssetAllocation =
            state.subAccount.isTypeAll ||
            state.subAccount.isNormal ||
            state.subAccount.isMargin;

        return SafeArea(
          child: PullToRefreshView(
            onRefresh: () => context.read<AssetSummaryCubit>().onRefresh(),
            child: SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SelectedSubAccountWidget(
                    subAccount:
                        context.read<AssetSummaryCubit>().state.subAccount,
                  ),

                  if (showAssetAllocation)
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Padding(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 8,
                          ),
                          child: Text(
                            'Phân bổ tài sản nắm giữ',
                            style: vpTextStyle.subtitle16.copyColor(
                              vpColor.textPrimary,
                            ),
                          ),
                        ),
                        AssetAllocationWidget(
                          chartData: state.asssetSummaryModel?.listReport ?? [],
                        ),
                      ],
                    ),

                  // Tiền và tài sản nắm giữ
                  buildChartMoneyAndAssetHoldingView(
                    state.asssetSummaryModel?.chartCashAndHolding ?? [],
                  ),

                  buildChartNavAndDebtView(),

                  buildChartAssetGrowthView(),
                  const SizedBox(height: 40),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _body() {
    return SafeArea(
      child: Column(
        children: [buildHeaderView(), Expanded(child: buildContentView())],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return VPScaffold(
      backgroundColor: vpColor.backgroundElevationMinus1,
      body: _body(),
    );
  }

  Widget buildChartAssetGrowthView() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Text(
            'Tăng trưởng tài sản',
            style: vpTextStyle.subtitle16?.copyWith(color: vpColor.textPrimary),
          ),
        ),
        BlocBuilder<AssetSummaryCubit, AssetSummaryState>(
          buildWhen: (p, c) => p.investmentGrowth != c.investmentGrowth,
          builder: (context, state) {
            return state.investmentGrowth == null
                ? const NoDataView()
                : AssetGrowthWidget(lstData: state.investmentGrowth!);
          },
        ),
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              InkWell(
                onTap:
                    () => context
                        .read<AssetSummaryCubit>()
                        .onDateTimeAssetChange(-1),
                child: Padding(
                  padding: const EdgeInsets.all(4),
                  child: Assets.icons.icArrowLeft.svg(
                    color: vpColor.iconPrimary,
                    width: 16,
                    height: 16,
                  ),
                ),
              ),
              BlocBuilder<AssetSummaryCubit, AssetSummaryState>(
                builder: (context, state) {
                  final bloc = context.watch<AssetSummaryCubit>();
                  return Text(
                    '${bloc.fromDate.toDate()} - ${bloc.toDate.toDate()}',
                    style: vpTextStyle.body14?.copyWith(
                      color: vpColor.textPrimary,
                    ),
                  );
                },
              ),
              InkWell(
                onTap:
                    () => context
                        .read<AssetSummaryCubit>()
                        .onDateTimeAssetChange(1),
                child: Padding(
                  padding: const EdgeInsets.only(top: 2),
                  child: Assets.icons.icArrowRight.svg(
                    package: 'vp_common',
                    colorFilter: ColorFilter.mode(
                      vpColor.iconPrimary,
                      BlendMode.srcIn,
                    ),
                    width: 16,
                    height: 16,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget buildChartNavAndDebtView() {
    return BlocBuilder<AssetSummaryCubit, AssetSummaryState>(
      builder: (contextSum, state) {
        final showNavAndDev =
            state.subAccount.isTypeAll ||
            state.subAccount.isNormal ||
            state.subAccount.isMargin;

        if (!showNavAndDev) return const SizedBox(height: 0);

        return Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Text(
                'Tài sản ròng và nợ',
                style: vpTextStyle.subtitle16.copyColor(vpColor.textPrimary),
              ),
            ),
            NetAssetsAndLiabilitiesWidget(
              chartData: state.asssetSummaryModel?.chartNetAssetAndDept ?? [],
              //     chartData: state.chartNetAssetAndDept,
            ),
          ],
        );
      },
    );
  }

  Widget buildChartMoneyAndAssetHoldingView(
    List<ChartAssetReportModel> chartData,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Text(
            'Tiền và tài sản nắm giữ',
            style: vpTextStyle.subtitle16.copyColor(vpColor.textPrimary),
          ),
        ),
        CashAndHoldingWidget(chartData: chartData),
      ],
    );
  }

  Widget buildHeaderView() {
    return HeaderWidget(
      title: S.current.asset_statistics,
      subTitle: S.current.asset_asset,
    );
  }

  @override
  bool get wantKeepAlive => true;
}
