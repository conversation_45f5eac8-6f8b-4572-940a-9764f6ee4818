import 'package:flutter/material.dart';
import 'package:stock_detail/gen/assets.gen.dart';
import 'package:stock_detail/stock_detail_navigator.dart';
import 'package:stock_detail/tabs/stock_trading_tab/chart/historical_chart/historical_chart_view.dart';
import 'package:stock_detail/tabs/stock_trading_tab/stock_trading_cubit.dart';
import 'package:stock_detail/widgets/circle_icon_view.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/widget/chip/chip_view.dart';
import 'package:vp_stock_common/model/enum/chart_period.dart';
import 'package:vp_stock_common/model/price_chart_model.dart';
import 'package:vp_stock_common/vp_stock_common.dart' hide CircleIconView;

import 'stock_detail_chart_cubit.dart';

class StockDetailChartView extends StatelessWidget {
  const StockDetailChartView({required this.stock, super.key});

  final StockInfoModel stock;

  String get symbol => stock.symbol;

  double get floorPrice => stock.floor!.toDouble();

  double get ceilingPrice => stock.ceiling!.toDouble();

  double get referencePrice => stock.refPrice!.toDouble();

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => StockDetailChartCubit(stock: stock)..loadTodayChartData(),
      child: DecoratedBox(
        decoration: BoxDecoration(
          color: vpColor.backgroundElevation0,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 8, 5, 0),
              child: SizedBox(
                height: 230,
                child:
                    BlocBuilder<StockDetailChartCubit, StockDetailChartState>(
                      buildWhen:
                          (preState, state) =>
                              preState.chartData != state.chartData,
                      builder: (context, state) {
                        if (state.chartPeriod == ChartPeriod.fiveMinutes) {
                          return IntradayChartView(
                            chartData: state.chartData,
                            floorPrice: floorPrice,
                            ceilingPrice: ceilingPrice,
                            refPrice: referencePrice,
                            refDashArray: const [8, 10],
                            showVolume: true,
                          );
                        }

                        return HistoricalChartView(
                          chartData:
                              state.chartData
                                  .map((e) => e as ChartData<PriceChartModel>)
                                  .toList(),
                          referencePrice: referencePrice,
                          itemVolumeColorBuilder:
                              (item) =>
                                  (item?.data as PriceChartModel?)
                                      ?.getVolumeItemColor(),
                        );
                      },
                    ),
              ),
            ),

            Padding(
              padding: const EdgeInsets.fromLTRB(16, 0, 5, 0),
              child: ChartPeriodView(symbol: symbol),
            ),
          ],
        ),
      ),
    );
  }
}

class ChartPeriodView extends StatelessWidget {
  const ChartPeriodView({required this.symbol, super.key});

  final String symbol;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: BlocSelector<
              StockDetailChartCubit,
              StockDetailChartState,
              ChartPeriod
            >(
              selector: (state) => state.chartPeriod,
              builder: (context, chartPeriod) {
                return Row(
                  children:
                      [
                            ChartPeriod.fiveMinutes,
                            ChartPeriod.oneWeek,
                            ChartPeriod.oneMonth,
                            ChartPeriod.threeMonth,
                            ChartPeriod.oneYear,
                          ]
                          .map(
                            (e) => Padding(
                              padding: const EdgeInsets.only(right: 8),
                              child: VPChipView.dynamic(
                                text: e.nameUI,
                                size: ChipSize.small,
                                style:
                                    chartPeriod == e
                                        ? ChipStyle.selected
                                        : ChipStyle.chipDefault,
                                onTap:
                                    () => context
                                        .read<StockDetailChartCubit>()
                                        .onChartPeriodChanged(e),
                              ),
                            ),
                          )
                          .toList(),
                );
              },
            ),
          ),
        ),

        IconButton(
          onPressed:
              () => context.read<StockTradingCubit>().onChartChange(true),
          icon: CircleIconView(
            child: VPStockDetailAssets.icons.icToggleChart.svg(
              width: 16,
              height: 16,
            ),
          ),
        ),

        IconButton(
          onPressed:
              () =>
                  stockDetailNavigator.openTradingView(context, symbol: symbol),
          icon: CircleIconView(
            child: VPStockDetailAssets.icons.icZoom.svg(width: 16, height: 16),
          ),
        ),
      ],
    );
  }
}
