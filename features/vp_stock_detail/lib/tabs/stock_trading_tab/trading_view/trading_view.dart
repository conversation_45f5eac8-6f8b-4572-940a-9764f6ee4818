import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:stock_detail/gen/assets.gen.dart';
import 'package:stock_detail/model/enum/trading_view_period.dart';
import 'package:stock_detail/stock_detail_navigator.dart';
import 'package:stock_detail/tabs/stock_trading_tab/trading_view/trading_view_cubit.dart';
import 'package:stock_detail/widgets/circle_icon_view.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';

class TradingView extends StatelessWidget {
  const TradingView({
    required this.symbol,
    this.actions,
    this.actionPadding = const EdgeInsets.fromLTRB(16, 0, 5, 0),
    this.padding = const EdgeInsets.fromLTRB(16, 8, 5, 0),
    super.key,
  });

  final String symbol;

  final EdgeInsets padding;

  final EdgeInsets actionPadding;

  final List<Widget>? actions;

  @override
  Widget build(BuildContext context) {
    return BlocProvider<TradingViewCubit>(
      create:
          (context) => TradingViewCubit(
            symbol: symbol,
            userId: context.read<AuthCubit>().userInfo?.userinfo?.custodycd,
          ),
      child: DecoratedBox(
        decoration: BoxDecoration(
          color: vpColor.backgroundElevation0,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          children: [
            Padding(
              padding: padding,
              child: SizedBox(
                height: 230,
                child: BlocBuilder<TradingViewCubit, TradingViewState>(
                  builder: (context, state) {
                    return VPWebView(
                      url: state.url,
                      gestureRecognizers:
                          <Factory<OneSequenceGestureRecognizer>>{
                            Factory<OneSequenceGestureRecognizer>(
                              () => EagerGestureRecognizer(),
                            ),
                          },
                      loadingBuilder: (_) {
                        return CircularProgressIndicator(
                          color: const Color(0xFF0059FF),
                          backgroundColor: context.colors.strokeNormal,
                        );
                      },
                      cookie:
                          state.accessToken != null
                              ? (
                                name: 'neo-access-token',
                                value: state.accessToken!,
                                domain: state.url,
                                path: '/',
                              )
                              : null,
                      backgroundColor: vpColor.backgroundElevation0,
                    );
                  },
                ),
              ),
            ),

            Padding(
              padding: actionPadding,
              child: ChartPeriodView(symbol: symbol, actions: actions),
            ),
          ],
        ),
      ),
    );
  }
}

class ChartPeriodView extends StatelessWidget {
  const ChartPeriodView({required this.symbol, this.actions, super.key});

  final String symbol;

  final List<Widget>? actions;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: BlocSelector<
              TradingViewCubit,
              TradingViewState,
              TradingViewPeriod
            >(
              selector: (state) => state.period,
              builder: (context, period) {
                return Row(
                  children:
                      TradingViewPeriod.values
                          .map(
                            (e) => Padding(
                              padding: const EdgeInsets.only(right: 8),
                              child: VPChipView.dynamic(
                                text: e.label,
                                size: ChipSize.small,
                                style:
                                    period == e
                                        ? ChipStyle.selected
                                        : ChipStyle.chipDefault,
                                onTap:
                                    () => context
                                        .read<TradingViewCubit>()
                                        .onPeriodChange(e),
                              ),
                            ),
                          )
                          .toList(),
                );
              },
            ),
          ),
        ),

        ...?actions,

        IconButton(
          onPressed:
              () => stockDetailNavigator.openTradingView(
                context,
                symbol: context.read<TradingViewCubit>().state.symbol,
                period: context.read<TradingViewCubit>().state.period,
              ),
          icon: CircleIconView(
            child: VPStockDetailAssets.icons.icZoom.svg(width: 16, height: 16),
          ),
        ),
      ],
    );
  }
}
