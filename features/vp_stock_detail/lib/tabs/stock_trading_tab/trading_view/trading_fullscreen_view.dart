import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:stock_detail/model/enum/trading_view_period.dart';
import 'package:stock_detail/tabs/stock_trading_tab/trading_view/trading_view_cubit.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';

class TradingViewFullScreen extends StatefulWidget {
  const TradingViewFullScreen({
    super.key,
    required this.symbol,
    this.period = TradingViewPeriod.oneDay,
  });

  final String symbol;

  final TradingViewPeriod period;

  @override
  State<TradingViewFullScreen> createState() => _TradingViewFullScreenState();
}

class _TradingViewFullScreenState extends State<TradingViewFullScreen> {
  @override
  void initState() {
    super.initState();

    SystemChrome.setPreferredOrientations([DeviceOrientation.landscapeRight]);
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider<TradingViewCubit>(
      create:
          (_) => TradingViewCubit(
            symbol: widget.symbol,
            period: widget.period,
            userId: context.read<AuthCubit>().userInfo?.userinfo?.custodycd,
          ),
      child: VPScaffold(
        body: Stack(
          children: [
            BlocSelector<TradingViewCubit, TradingViewState, String>(
              selector: (state) => state.url,
              builder: (context, url) {
                return VPWebView(
                  url: url,
                  backgroundColor: vpColor.backgroundElevation0,
                  loadingBuilder: (_) {
                    return CircularProgressIndicator(
                      color: const Color(0xFF0059FF),
                      backgroundColor: context.colors.strokeNormal,
                    );
                  },
                );
              },
            ),

            Positioned(
              bottom: 20,
              right: 10,
              child: IconButton(
                onPressed: () => Navigator.of(context).pop(),
                icon: Icon(Icons.close, size: 32, color: vpColor.iconPrimary),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);

    super.dispose();
  }
}
