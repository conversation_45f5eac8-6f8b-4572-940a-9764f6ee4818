import 'dart:convert';
import 'dart:io';

import 'package:vp_auth/core/constant/onboarding_path_api.dart';
import 'package:vp_auth/core/repository/onboarding_repository.dart';
import 'package:vp_auth/model/sign_up/model/nfc/nfc_entity.dart';
import 'package:vp_auth/model/sign_up/model/read_nfc_request.dart';
import 'package:vp_auth/model/sign_up/register/account_laundering_money_item_question_model.dart';
import 'package:vp_auth/model/sign_up/register/onboarding/models/onboarding_users_acc_num_obj.dart';
import 'package:vp_auth/model/sign_up/register/onboarding/models/onboarding_users_banks_obj.dart';
import 'package:vp_auth/model/sign_up/register/onboarding/models/onboarding_users_brokers_obj.dart';
import 'package:vp_auth/model/sign_up/register/onboarding/models/onboarding_users_ekyc_action_obj.dart';
import 'package:vp_auth/model/sign_up/register/onboarding/models/onboarding_users_generate_contract_obj.dart';
import 'package:vp_auth/model/sign_up/register/onboarding/models/otp_verification_obj.dart';
import 'package:vp_auth/model/upload_ocr/onboarding_users_ekyc.dart';
import 'package:vp_auth/utils/base_response.dart';
import 'package:vp_common/error/transform_error.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';

class OnboardingRepositoryIpml extends OnboardingRepository {
  final RestClient _restClient;
  final RestClient _baseClient;

  OnboardingRepositoryIpml(
      {required RestClient restClient, required RestClient baseClient})
      : _restClient = restClient,
        _baseClient = baseClient;

  /// Kiem tra va genOTP tu so dien thoai
  @override
  Future<OnboardingResponse?> noAuthUserOtP(
      {String phoneNumber = '',
      String? sessionId,
      String? recaptchaToken}) async {
    OnboardingResponse? baseResponse;
    try {
      final param = {
        'phone': phoneNumber,
        "platfrom": Platform.isAndroid ? 'android' : 'ios',
        'sessionId': sessionId,
        "recaptchaAction": "signup",
        'recaptchaToken': recaptchaToken,
        'verifyCaptcha': true,
      };
      final response = await _restClient.dio.post(
          OnboardingPathAPI.checkPhoneNumber,
          data: param,
          options: Options(headers: {KeyAPI.contentType: KeyAPI.appJson}));
      baseResponse = OnboardingResponse.fromJson(
          response.data, response.statusCode,
          data: DataHelper.getMap(response.data));
    } catch (e) {
      throw HandleError.from(e);
    }
    return baseResponse;
  }

  // API xac thuc OTP
  @override
  Future<OnboardingResponse<OtpVerificationResponseObj>?> otpVerification(
      OtpVerificationRequestObj param) async {
    OnboardingResponse<OtpVerificationResponseObj>? baseResponse;
    try {
      final mapHeader = {KeyAPI.contentType: KeyAPI.appJson, KeyAPI.from: 'M'};
      final response = await _restClient.dio.post(
          OnboardingPathAPI.otpVerification,
          data: param.toJson(),
          options: Options(headers: mapHeader));
      OtpVerificationResponseObj? obj;
      if (DataHelper.getMap(response.data) != null) {
        obj = OtpVerificationResponseObj.fromJson(
            DataHelper.getMap(response.data));
      }
      baseResponse = OnboardingResponse<OtpVerificationResponseObj>.fromJson(
          response.data, response.statusCode,
          data: obj);
      if (baseResponse.isSuccess()) {
        Session().setOnboardingToken('${obj?.tokenType} ${obj?.accessToken}');
      }
    } catch (e) {
      throw HandleError.from(e);
    }
    return baseResponse;
  }

  // API update email
  @override
  Future<OnboardingResponse?> onboardingUsersEmail(String email) async {
    OnboardingResponse? baseResponse;
    try {
      final response = await _restClient.dio.put(
          OnboardingPathAPI.getOnboardingUserEmail(email),
          options: optionsOB());
      baseResponse =
          OnboardingResponse.fromJson(response.data, response.statusCode);
    } catch (e) {
      throw HandleError.from(e);
    }
    return baseResponse;
  }

  // API update bank acc
  @override
  Future<OnboardingResponse?> onboardingUsersBank(
      OnboardingUsersBankRequestObj param) async {
    OnboardingResponse? baseResponse;
    try {
      final response = await _restClient.dio.put(
          OnboardingPathAPI.onboardingUserBank,
          data: param.toJson(),
          options: optionsOB());

      baseResponse =
          OnboardingResponse.fromJson(response.data, response.statusCode);
    } catch (e) {
      throw HandleError.from(e);
    }
    return baseResponse;
  }

  // Account check brokers
  @override
  Future<OnboardingResponse<OnboardingUsersBrokersResponseObj>?>
      onboardingUsersBrokers(String accountNo) async {
    OnboardingResponse<OnboardingUsersBrokersResponseObj>? baseResponse;
    try {
      final param = {'accountNo': accountNo};
      final response = await _restClient.dio.get(
          OnboardingPathAPI.onboardingUsersBrokers,
          queryParameters: param,
          options: optionsOB());
      OnboardingUsersBrokersResponseObj? obj;

      if (DataHelper.getMapFromList(response.data) != null) {
        obj = OnboardingUsersBrokersResponseObj.fromJson(
            DataHelper.getMapFromList(response.data));
      }

      baseResponse =
          OnboardingResponse<OnboardingUsersBrokersResponseObj>.fromJson(
        response.data,
        response.statusCode,
        data: obj,
      );
    } catch (e) {
      throw HandleError.from(e);
    }
    return baseResponse;
  }

  // API update broker
  @override
  Future<OnboardingResponse?> onboardingUsersPutBroker(
      {String brokerNeeded = '', String brokerNo = ''}) async {
    OnboardingResponse? baseResponse;
    try {
      final response = await _restClient.dio.put(
          OnboardingPathAPI.getOnBoardingUsersPutBroker(
              brokerNeeded: brokerNeeded, brokerNo: brokerNo),
          options: optionsOB());
      baseResponse =
          OnboardingResponse.fromJson(response.data, response.statusCode);
    } catch (e) {
      throw HandleError.from(e);
    }
    return baseResponse;
  }

  // API OCR, day 3 anh len
  @override
  Future<OnboardingResponse<OnboardingUsersEkycResponseObj>?>
      onboadingUsersEkyc({
    required OnboardingUsersEkycRequestObj param,
    required String onboardingToken,
    required String apiPath,
  }) async {
    OnboardingResponse<OnboardingUsersEkycResponseObj>? baseResponse;
    try {
      var client = http.Client();

      final data = await client.post(
        Uri.parse(apiPath),
        body: jsonEncode(param.toJson()),
        headers: {
          KeyAPI.contentType: KeyAPI.appJson,
          KeyAPI.authorization: onboardingToken
        },
      );

      final jsonData = jsonDecode(utf8.decode(data.bodyBytes));

      OnboardingUsersEkycResponseObj? obj;

      if (DataHelper.getMap(jsonData) != null) {
        obj = OnboardingUsersEkycResponseObj.fromJson(
            DataHelper.getMap(jsonData));
      }

      baseResponse =
          OnboardingResponse<OnboardingUsersEkycResponseObj>.fromJson(
        jsonData,
        data.statusCode,
        data: obj,
      );
    } catch (e) {
      throw HandleError.from(e);
    }
    return baseResponse;
  }

  // Update thong tin sau khi chinh sua thong tin OCR
  @override
  Future<OnboardingResponse?> onboardingUsersOcr(
      OnboardingUsersUpdateOcrRequestObj param) async {
    OnboardingResponse? baseResponse;
    try {
      final response = await _restClient.dio.put(
          OnboardingPathAPI.onboardingUserOCR,
          data: param.toJson(),
          options: optionsOB());
      baseResponse =
          OnboardingResponse.fromJson(response.data, response.statusCode);
    } catch (e) {
      throw HandleError.from(e);
    }
    return baseResponse;
  }

  // API gen so tieu khoan luu ky
  @override
  Future<OnboardingResponse<OnboardingUsersAccNumResponseObj>?>
      onboardingUsersAccNum(String value, {bool isRandom = false}) async {
    OnboardingResponse<OnboardingUsersAccNumResponseObj>? baseResponse;
    try {
      final param = isRandom ? {'': ''} : {'mobile': value};
      final response = await _restClient.dio.get(
          OnboardingPathAPI.onboardingUsersAccNum,
          queryParameters: param,
          options: optionsOB());
      OnboardingUsersAccNumResponseObj? obj;
      if (DataHelper.getMapFromList(response.data) != null) {
        obj = OnboardingUsersAccNumResponseObj.fromJson(
            DataHelper.getMapFromList(response.data));
      }
      baseResponse =
          OnboardingResponse<OnboardingUsersAccNumResponseObj>.fromJson(
              response.data, response.statusCode,
              data: obj);
    } catch (e) {
      throw HandleError.from(e);
    }
    return baseResponse;
  }

  // Kiem tra so tieu khoan luu ky ton tai va update
  @override
  Future<OnboardingResponse?> onboardingUsersAccount(String accountNo) async {
    OnboardingResponse? baseResponse;
    try {
      final response = await _restClient.dio.put(
          OnboardingPathAPI.getOnboardingUserAccount(accountNo),
          options: optionsOB());
      baseResponse =
          OnboardingResponse.fromJson(response.data, response.statusCode);
    } catch (e) {
      throw HandleError.from(e);
    }
    return baseResponse;
  }

  /// API lay danh sach hanh dong
  @override
  Future<OnboardingResponse<OnboardingUsersAccEkycActionResponseObj>?>
      onboardingUsersEkycAction() async {
    OnboardingResponse<OnboardingUsersAccEkycActionResponseObj>? baseResponse;
    try {
      final response = await _restClient.dio.get(
          OnboardingPathAPI.onboardingUsersEkycActions,
          options: optionsOB());
      OnboardingUsersAccEkycActionResponseObj? obj;
      if (DataHelper.getMap(response.data) != null) {
        obj = OnboardingUsersAccEkycActionResponseObj.fromJson(
            DataHelper.getMap(response.data));
      }
      baseResponse =
          OnboardingResponse<OnboardingUsersAccEkycActionResponseObj>.fromJson(
              response.data, response.statusCode,
              data: obj);
    } catch (e) {
      throw HandleError.from(e);
    }
    return baseResponse;
  }

  // API xac thuc khuon mat
  @override
  Future<OnboardingResponse?> onboardingUsersEkycFace({
    required OnboardingUsersEkycFaceRequestObj param,
    required String onboardingToken,
    required String apiPath,
  }) async {
    try {
      var client = http.Client();

      final data = await client.post(
        Uri.parse(apiPath),
        body: jsonEncode(param.toJson()),
        headers: {
          KeyAPI.contentType: KeyAPI.appJson,
          KeyAPI.authorization: onboardingToken
        },
      );

      final jsonData = jsonDecode(utf8.decode(data.bodyBytes));

      final beResponse = BEBaseResponse.fromJson(jsonData);

      if (beResponse.isSuccess()) {
        return OnboardingResponse.fromJson(jsonData, data.statusCode);
      }

      throw ResponseError.fromJson(jsonData);
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
      throw HandleError.from(e);
    }
  }

  // API tao tai khoan
  @override
  Future<OnboardingResponse?> onboardingUsers() async {
    OnboardingResponse? baseResponse;
    try {
      final response = await _restClient.dio
          .post(OnboardingPathAPI.onboardingUsers, options: optionsOB());
      baseResponse = OnboardingResponse.fromJson(
          response.data, response.statusCode,
          data: DataHelper.getMap(response.data));
    } catch (e) {
      throw HandleError.from(e);
    }
    return baseResponse;
  }

  // API khoi tao hop dong
  @override
  Future<OnboardingResponse<List<OnboardingUsersGenerateContractObj>>?>
      onboardingContractGeneration() async {
    OnboardingResponse<List<OnboardingUsersGenerateContractObj>>? baseResponse;
    try {
      final response = await _restClient.dio.post(
          OnboardingPathAPI.onboardingContractGeneration,
          options: optionsOB());

      List<OnboardingUsersGenerateContractObj> listData = [];
      if (DataHelper.getMapList(response.data) != null) {
        for (var json in DataHelper.getMapList(response.data)) {
          listData.add(OnboardingUsersGenerateContractObj.fromJson(json));
        }
      }
      baseResponse = OnboardingResponse.fromJson(
          response.data, response.statusCode,
          data: listData);
    } catch (e) {
      throw HandleError.from(e);
    }
    return baseResponse;
  }

  // Xem file hop dong
  @override
  Future<OnboardingResponse<OnboardingUsersFileContractObj>?> onboardingFiles(
      String requestId) async {
    OnboardingResponse<OnboardingUsersFileContractObj>? baseResponse;
    try {
      final response = await _baseClient.dio.get(
          OnboardingPathAPI.onboardingFiles,
          queryParameters: {'requestId': requestId},
          options: optionsOB());
      OnboardingUsersFileContractObj? obj;
      if (DataHelper.getMap(response.data) != null) {
        obj = OnboardingUsersFileContractObj.fromJson(
            DataHelper.getMap(response.data));
      }
      baseResponse = OnboardingResponse.fromJson(
          response.data, response.statusCode,
          data: obj);
    } catch (e) {
      throw HandleError.from(e);
    }
    return baseResponse;
  }

  Options optionsOB({bool withDeviceSource = false}) {
    Map<String, dynamic> headers = {
      KeyAPI.contentType: KeyAPI.appJson,
      KeyAPI.authorization: Session().onboardingToken
    };
    if (withDeviceSource) {
      headers[KeyAPI.deviceSource] = Platform.isAndroid ? 'android' : 'ios';
    }
    return Options(
      headers: headers,
    );
  }

  @override
  Future<bool> genOnboardingOTP() async {
    try {
      final response = await _restClient.dio.post(
        OnboardingPathAPI.genOnboardingOTP,
        options: optionsOB(),
      );

      final result = BEBaseResponse.fromJson(response.data);

      if (result.isSuccess()) {
        return true;
      }

      throw ResponseError.fromDioResponse(response);
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<Response> onboardingUsersNew(String otp) async {
    try {
      final response = await _restClient.dio.post(
        OnboardingPathAPI.openAccount,
        data: {'otp': otp},
        options: optionsOB(),
      );

      final result = OnboardingResponse.fromJson(
        response.data,
        response.statusCode,
      );

      if (result.isSuccess()) return response;

      throw ResponseError.fromDioResponse(response);
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future onUpdateReferenceCode(String code) async {
    try {
      var formData = FormData.fromMap({'referrer': code});

      final response = await _restClient.dio.put(
        OnboardingPathAPI.updateReferenceCode,
        data: formData,
        options: optionsOB(),
      );

      await _restClient.dio.put(
        OnboardingPathAPI.updateDefaultBroker,
        data: FormData.fromMap({'brokerNeeded': 0}),
        options: optionsOB(),
      );

      final baseResponse = OnboardingResponse.fromJson(
        response.data,
        response.statusCode,
        data: DataHelper.getMap(response.data),
      );

      if (!baseResponse.isSuccess()) {
        throw ResponseError.fromDioResponse(response);
      }
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future onSubmitMoneyLaundering(Map<String, dynamic> data) async {
    try {
      final response = await _restClient.dio.post(
        OnboardingPathAPI.submitMoneyLaundering,
        data: data,
        options: optionsOB(withDeviceSource: true),
      );

      final result = BEBaseResponse.fromJson(response.data);

      if (result.isSuccess()) {
        return true;
      }

      throw ResponseError.fromDioResponse(response);
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<String> getUrlFileContractRegisterBank() async {
    try {
      final response = await _restClient.dio.post(
        OnboardingPathAPI.genContractRegister,
        options: optionsOB(),
      );

      final result = BEBaseResponse.fromJson(response.data);

      if (result.isSuccess() && (result.data?.isNotEmpty ?? false)) {
        final requestId = result.data[0]['requestId'];
        final url = await _getFile(requestId);
        return url;
      }

      throw ResponseError.fromDioResponse(response);
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  Future<String> _getFile(String requestId) async {
    try {
      final response = await _baseClient.dio.get(OnboardingPathAPI.getFile,
          options: optionsOB(), queryParameters: {"requestId": requestId});

      final result = BEBaseResponse.fromJson(response.data);

      if (result.isSuccess() && result.data != null) {
        return result.data["url"];
      }

      throw ResponseError.fromDioResponse(response);
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<List<MoneyLaunderingItemQuestionModel>>
      getListQuestionLaunderingMoneyModel(String questionCode) async {
    try {
      final response =
          await _restClient.dio.get(OnboardingPathAPI.listQuestionMoney,
              queryParameters: {
                "questionCode": questionCode,
              },
              options: optionsOB());
      if (response.data != null && response.data['answers'] != null) {
        List<MoneyLaunderingItemQuestionModel> listData = [];
        for (var json in response.data['answers']) {
          listData.add(MoneyLaunderingItemQuestionModel.fromJson(json));
        }
        return listData;
      }
      throw ResponseError.fromDioResponse(response);
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<BEBaseResponse> checkVpbankAccountExist() async {
    try {
      final response = await _restClient.dio.get(
        OnboardingPathAPI.checkExistAccountBank,
        options: optionsOB(withDeviceSource: true),
      );
      return BEBaseResponse.fromJson(response.data);
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future agreeOpenAccountBank({bool openBankAcc = true}) async {
    try {
      final response = await _restClient.dio.put(
        OnboardingPathAPI.agreeOpenAccountBank,
        options: optionsOB(),
        data: {"vpbankOpenStatus": openBankAcc ? "Y" : "N"},
      );
      final result = BEBaseResponse.fromJson(response.data);
      if (result.isSuccess()) {
        return;
      }

      throw ResponseError.fromDioResponse(response);
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<OnboardingResponse> uploadSignature({required String apiPath}) async {
    try {
      FormData formData = FormData.fromMap({
        "file": await MultipartFile.fromFile(
          apiPath,
          filename: 'SignatureImageFile',
        ),
      });
      dlog(apiPath);
      final response = await _restClient.dio.post(
        OnboardingPathAPI.signatureImage,
        options: optionsOB(),
        data: formData,
      );
      final result =
          OnboardingResponse.fromJson(response.data, response.statusCode);
      // return result;
      // return result;
      // print(result);
      return result;
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<BEBaseResponse> confirmOCRUpdate() async {
    try {
      final response = await _restClient.dio.put(
        OnboardingPathAPI.confirmOCRUpdate(),
        options: optionsOB(withDeviceSource: true),
      );
      return BEBaseResponse.fromJson(response.data);
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<NFCEntity> readNFCExternal(
      {required ReadNFCRequest nfcRequest}) async {
    try {
      final response = await _restClient.dio.post(
        OnboardingPathAPI.readNFCOnboarding,
        data: nfcRequest.toJson(),
        options: optionsOB(),
      );

      final beResponse = BEBaseResponse.fromJson(response.data);
      if (beResponse.status == 200) {
        return NFCEntity.fromJson(response.data['data']);
      }
      throw ResponseError.fromDioResponse(response);
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<BEBaseResponse> saveNFCLog(
      {required SaveLogNFCRequest request}) async {
    try {
      final response = await _restClient.dio.put(
        OnboardingPathAPI.saveNFCLogOnboarding,
        data: request.toJson(),
        options: optionsOB(),
      );

      final beResponse = BEBaseResponse.fromJson(response.data);
      return beResponse;
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<dynamic> updateAvatar({String? pathFile}) async {
    try {
      final fileName = pathFile ?? ''.split('/').last;
      FormData data = FormData.fromMap({
        "file": await MultipartFile.fromFile(
          pathFile ?? '',
          filename: fileName,
        )
      });
      final response = await _restClient.dio.put(
        OnboardingPathAPI.faceFont,
        data: data,
        options: optionsOB(),
      );
      final beResponse = BEBaseResponse.fromJson(response.data);
      if (beResponse.isSuccess()) return beResponse;
      throw ResponseError.fromDioResponse(response);
    } catch (e) {
      dlog(e);
      throw HandleError.from(e);
    }
  }

  @override
  Future verifyNFC() async {
    try {
      final response = await _restClient.dio.post(
        OnboardingPathAPI.verifyNFC,
        options: optionsOB(withDeviceSource: true),
      );
      final beResponse = BEBaseResponse.fromJson(response.data);
      return beResponse;
    } catch (e) {
      throw HandleError.from(e);
    }
  }
}
