
import 'package:dio/dio.dart';
import 'package:vp_auth/core/constant/account_path_api.dart';
import 'package:vp_auth/core/repository/account_register_repository.dart';
import 'package:vp_auth/model/sign_up/register/account_add_register_obj.dart';
import 'package:vp_auth/model/sign_up/register/account_bank_branch.dart';
import 'package:vp_auth/model/sign_up/register/account_bank_list.dart';
import 'package:vp_auth/model/sign_up/register/account_bank_reg_open_acc_obj.dart';
import 'package:vp_auth/model/sign_up/register/account_broker_info.dart';
import 'package:vp_auth/model/sign_up/register/account_check_exist_obj.dart';
import 'package:vp_auth/model/sign_up/register/account_checkacc_info.dart';
import 'package:vp_auth/model/sign_up/register/account_ekyc_ai_obj.dart';
import 'package:vp_auth/model/sign_up/register/account_gen_otp.dart';
import 'package:vp_auth/model/sign_up/register/account_suggest_custcd_obj.dart';
import 'package:vp_auth/model/sign_up/register/account_verify_otp.dart';
import 'package:vp_common/error/handle_error.dart';
import 'package:vp_common/error/transform_error.dart';
import 'package:vp_common/widget/log_api/models/app_base_response.dart';


class AccountRegisterRepositoryImpl extends AccountRegisterRepository {
  final Dio _restClient;

  AccountRegisterRepositoryImpl({required Dio restClient})
      : _restClient = restClient;

  // Danh sach ngan hang
  @override
  Future<List<AccountBankListResponse>> getListBank() async {
    List<AccountBankListResponse> listBank = [];
    try {
      Response response = await _restClient.get(AccountPathAPI.bankList);

      final result = AppBaseResponse.fromJson(response.data);
      if (result.isSuccess() && result.data is List) {
        for (var jsonObj in result.data) {
          listBank.add(AccountBankListResponse.fromJson(jsonObj));
        }
        AppCache().setListBank(listBank);
      } else {
        throw ResponseError.fromDioResponse(response);
      }
    } on DioException catch (e) {
      throw HandleError.from(e);
    }
    return listBank;
  }

  // GenOTP
  @override
  Future<AccountGenOtpResponse?> genOTP(AccountGenOtpRequest param) async {
    AccountGenOtpResponse? genOTPObj;
    try {
      Response response = await _restClient
          .post(AccountPathAPI.tranGenOTP, data: param.toJson());
      final result = AppBaseResponse.fromJson(response.data);
      if (result.isSuccess()) {
        genOTPObj = AccountGenOtpResponse.fromJson(result.data);
      } else {
        throw ResponseError.fromDioResponse(response);
      }
    } on DioException catch (e) {
      throw HandleError.from(e);
    }
    return genOTPObj;
  }

  // Xac thuc SMS OTP voi so dien thoai
  @override
  Future<AppBaseResponse?> verifyOTP(AccountVerifyOtpRequest param) async {
    AppBaseResponse? appResponseObj;
    try {
      Response response = await _restClient
          .post(AccountPathAPI.tranVerifyPhone, data: param.toJson());
      appResponseObj = AppBaseResponse.fromJson(response.data);
    } on DioException catch (e) {
      throw HandleError.from(e);
    }
    return appResponseObj;
  }

  // Kiem tra so dien thoai email
  @override
  Future<AppBaseResponse?> checkAccountInfo(
      AccountCheckAccInfoRequestObj param) async {
    AppBaseResponse? appResponseObj;
    try {
      Response response = await _restClient
          .post(AccountPathAPI.checkAccountInfo, data: param.toJson());
      appResponseObj = AppBaseResponse.fromJson(response.data);
    } on DioException catch (e) {
      throw HandleError.from(e);
    }
    return appResponseObj;
  }

  // Lay danh sach chi nhanh ngan hang
  @override
  Future<List<AccountBankBranchResponseObj>> getListBankBranch(
      int bankId) async {
    List<AccountBankBranchResponseObj> listBankBranch = [];
    try {
      Response response = await _restClient
          .get(AccountPathAPI.bankBranch, queryParameters: {"bankid": bankId});
      final result = AppBaseResponse.fromJson(response.data);
      if (result.isSuccess() && result.data is List) {
        for (var jsonObj in result.data) {
          listBankBranch.add(AccountBankBranchResponseObj.fromJson(jsonObj));
        }
      } else {
        throw ResponseError.fromDioResponse(response);
      }
    } on DioException catch (e) {
      throw HandleError.from(e);
    }
    return listBankBranch;
  }

  // Lay thong tin moi gioi
  @override
  Future<AccountBrokerInfoResponseObj?> getBrokerInfo(String custID) async {
    AccountBrokerInfoResponseObj? brokerInfoObj;
    try {
      Response response = await _restClient.get(AccountPathAPI.brokerInfo,
          queryParameters: {"custodycd": custID});
      final result = AppBaseResponse.fromJson(response.data);
      if (result.isSuccess() && result.data is List) {
        for (var jsonObj in result.data) {
          brokerInfoObj = AccountBrokerInfoResponseObj.fromJson(jsonObj);
          break;
        }
      } else {
        throw ResponseError.fromDioResponse(response);
      }
    } on DioException catch (e) {
      throw HandleError.from(e);
    }
    return brokerInfoObj;
  }

  // Api lay ReqID
  @override
  Future<AccountBankRegOpenAccResponseObj?> regOpenAccount(
      Map<String, dynamic> param) async {
    AccountBankRegOpenAccResponseObj? regOpenAccObj;
    try {
      Response response = await _restClient
          .post(AccountPathAPI.regOpenAccount, data: param);
      final result = AppBaseResponse.fromJson(response.data);
      if (result.isSuccess() && result.data != null) {
        regOpenAccObj = AccountBankRegOpenAccResponseObj.fromJson(result.data);
      } else {
        throw ResponseError.fromDioResponse(response);
      }
    } on DioException catch (e) {
      throw HandleError.from(e);
    }
    return regOpenAccObj;
  }

  // API check sau khi OCR
  @override
  Future<AppBaseResponse?> checkOpenAccount(Map<String, dynamic> param) async {
    AppBaseResponse? appResponseObj;
    try {
      Response response = await _restClient
          .post(AccountPathAPI.checkOpenAccount, data: param);
      appResponseObj = AppBaseResponse.fromJson(response.data);
    } on DioException catch (e) {
      throw HandleError.from(e);
    }
    return appResponseObj;
  }

  // API goi y so tai khoan
  @override
  Future<AccountSuggestCustcdResponseObj?> suggestCustCd(
      AccountSuggestCustcdRequestObj param) async {
    AccountSuggestCustcdResponseObj? suggestCustcdResponseObj;
    try {
      Response response = await _restClient
          .get(AccountPathAPI.suggestCustCd, queryParameters: param.toJson());
      final result = AppBaseResponse.fromJson(response.data);
      if (result.isSuccess() && result.data != null) {
        suggestCustcdResponseObj =
            AccountSuggestCustcdResponseObj.fromJson(result.data);
      } else {
        throw ResponseError.fromDioResponse(response);
      }
    } on DioException catch (e) {
      throw HandleError.from(e);
    }
    return suggestCustcdResponseObj;
  }

  // API kiem tra tai khoan da ton tai hay chua
  @override
  Future<AppBaseResponse?> checkExitsAccount(
      AccountCheckExitsRequestObj param) async {
    AppBaseResponse? appResponseObj;
    try {
      Response response = await _restClient
          .post(AccountPathAPI.checkExistAccount, data: param.toJson());
      appResponseObj = AppBaseResponse.fromJson(response.data);
    } on DioException catch (e) {
      throw HandleError.from(e);
    }
    return appResponseObj;
  }

  // API day anh len sever
  @override
  Future<AppBaseResponse?> ekycIdattach(
      {String reqId = '', String path = '', String type = ''}) async {
    AppBaseResponse? appResponseObj;
    try {
      final fileName = path.split('/').last;
      FormData data = FormData.fromMap({
        "idattach": await MultipartFile.fromFile(
          path,
          filename: fileName,
        ),
        "reqid": reqId,
        "type": type,
      });

      Response response =
          await _restClient.post(AccountPathAPI.idattach, data: data);
      appResponseObj = AppBaseResponse.fromJson(response.data);
    } on DioException catch (e) {
      throw HandleError.from(e);
    }
    return appResponseObj;
  }

  // API kiem tra chi so ekyc
  @override
  Future<AppBaseResponse?> checkEkycAi(AccountEkycAiRequestObj param) async {
    AppBaseResponse? appResponseObj;
    try {
      Response response = await _restClient
          .post(AccountPathAPI.checkEkycAi, data: param.toCheckEkycJson());
      appResponseObj = AppBaseResponse.fromJson(response.data);
    } on DioException catch (e) {
      throw HandleError.from(e);
    }
    return appResponseObj;
  }

  // API mo tai khoan
  @override
  Future<AppBaseResponse?> openAccount(AccountEkycAiRequestObj param) async {
    AppBaseResponse? appResponseObj;
    try {
      Response response = await _restClient
          .post(AccountPathAPI.openAccount, data: param.toOpenAccountJson());
      appResponseObj = AppBaseResponse.fromJson(response.data);
    } on DioException catch (e) {
      throw HandleError.from(e);
    }
    return appResponseObj;
  }

  // API bo sung cac dich vu dang ky them
  @override
  Future<AppBaseResponse?> addRegister(
      AccountAddRegisterRequestObj param) async {
    AppBaseResponse? appResponseObj;
    try {
      Response response = await _restClient
          .post(AccountPathAPI.addRegister, data: param.toJson());
      appResponseObj = AppBaseResponse.fromJson(response.data);
    } on DioException catch (e) {
      throw HandleError.from(e);
    }
    return appResponseObj;
  }
}
