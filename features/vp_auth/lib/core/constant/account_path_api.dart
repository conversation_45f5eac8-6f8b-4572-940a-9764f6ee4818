const vpAuth = "vp_auth";

class AccountPathAPI {
  static const login = "/auth/token";
  static const userInfo = "/flex/user/info";
  static const accounts = "/flex/accountsAll";
  static const derivativeAccount = "/derivative/external/v1/inquiry/accounts";
  static const verificationInfo = "/iam/external/users/info";

  /*----- Register ---- */
  static const tranVerifyPhone = '/tran/verifyPhone';
  static const tranGenOTP = '/tran/genOtp';
  static const bankList = '/noauth/inq/banklist';
  static const checkAccountInfo = '/ekyc/checkAccountInfo';
  static const bankBranch = '/inq/bankBranch';
  static const brokerInfo = '/ekyc/brokerInfo';
  static const regOpenAccount = '/ekyc/regOpenAccount';
  static const checkOpenAccount = '/ekyc/checkOpenAccount';
  static const suggestCustCd = '/ekyc/suggestCustodycd';
  static const checkExistAccount = '/ekyc/checkExistAccount';

  static const idattach = '/ekyc/idattach';
  static const checkEkycAi = '/ekyc/checkEkycAi';
  static const openAccount = '/ekyc/openAccount';
  static const addRegister = '/ekyc/addRegister';

  /*----- Sign In ---- */

  /*----- Forgot Pass ---- */
  static const genResetPass = '/sso/genResetPass';
  static const checkChangePass = '/tran/checkChangePass';
  static const changePass = '/tran/changePass';

  /*----- Forgot Pass IAM ----- */
  static const genOTPResetPassword =
      '/no-auth/users/password/otp'; //GenOTP ResetPass
  static const genOTPResetPasswordV2 =
      '/iam/no-auth/v2/users/password/otp'; //GenOTP ResetPass
  static const verifyOTPResetPassword =
      '/iam/no-auth/users/password/otp-verification'; // Verify OTP
  static const passwordModification =
      '/iam/external/users/password/modification'; // Change pass

  /*----- Forgot Pin IAM ----- */
  static const genOTPForgotPin = '/iam/external/users/trading-password/forgotten';
  static const changePin = '/iam/external/users/trading-password/modification';

  /*----- Change Pass And Pin For First ---- */
  static const changePassFirst = '/iam/external/users/password';
  static const changePinFirst = '/iam/external/users/trading-password';

  //thông tin tài khoản khách hàng
  static const custinfo = '/iam/external/account';

  //TODO fix for call api
  static const userSettings =
      'https://neopro-uat.vpbanks.com.vn/neo-inv-customer/public/v1/users/userSettings';

  static const confirmOCRUpdate = '/iam/external/users/ekyc-modification/confirm';
}
