const vpTrading = 'vp_trading';

enum StockHoldingSortEnum { stockCode, costPrice, volume, profit }

enum FocusKeyboard {
  takeProfit,
  stopLoss,
  conditionStopLoss,
  range,
  stepPrice,
  slippage,
  trigger,
  priceActivation,
  price,
  volume,
  none,
}

enum StockHoldingStatusEnum {
  increase,
  decrease,
  none;

  StockHoldingStatusEnum next() {
    if (this == increase) {
      return decrease;
    }

    if (this == decrease) {
      return none;
    }

    return increase;
  }
}

enum StockHoldingProfitEnum {
  all('Lãi/lỗ dự kiến'),
  percent('% lãi lỗ');

  const StockHoldingProfitEnum(this.name);

  final String name;

  StockHoldingProfitEnum next() {
    if (this == all) {
      return percent;
    }

    return all;
  }

  StockHoldingProfitEnum previous() {
    if (this == all) {
      return percent;
    }

    return all;
  }
}

// Max value save symbol
const int maxValueSaveSymbol = 10;
// key sharePerfs
const keySymbolTradingCurrent = "symbol_trading_current";

const conditionalOrderGuideUrl =
    "https://hdsd.vpbanks.com.vn/phai-sinh/danh-muc-vi-the/cai-dat-gia-sl-tp-tren-danh-muc";
