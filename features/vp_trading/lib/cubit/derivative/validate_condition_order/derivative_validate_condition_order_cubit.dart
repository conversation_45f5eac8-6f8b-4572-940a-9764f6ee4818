import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_stock_common/vp_stock_common.dart';
import 'package:vp_trading/core/constant/constants.dart';
import 'package:vp_trading/core/constant/derivative_app_configs.dart';
import 'package:vp_trading/core/constant/order_type.dart';
import 'package:vp_trading/core/extension/ext.dart';
import 'package:vp_trading/cubit/derivative/validate_order/derivative_validate_order_cubit.dart';
import 'package:vp_trading/utils/condition_command_util.dart';

part 'derivative_validate_condition_order_state.dart';

enum ErrorStepPrice { none, empty, init }

enum ErrorRange { none, empty, init }

extension ErrorStepPriceeExtension on ErrorStepPrice {
  bool get isError {
    return this != ErrorStepPrice.none &&
        this != ErrorStepPrice.empty &&
        this != ErrorStepPrice.init;
  }

  String get message {
    switch (this) {
      case ErrorStepPrice.empty:
        return 'Vui lòng nhập bước giá';
      case ErrorStepPrice.none:
      case ErrorStepPrice.init:
        return '';
    }
  }
}

extension ErrorRangeExtension on ErrorRange {
  bool get isError {
    return this != ErrorRange.none &&
        this != ErrorRange.empty &&
        this != ErrorRange.init;
  }

  String get message {
    switch (this) {
      case ErrorRange.empty:
        return 'Vui lòng nhập biên độ';
      case ErrorRange.none:
      case ErrorRange.init:
        return '';
    }
  }
}

class DerivativeValidateConditionOrderCubit
    extends Cubit<DerivativeValidateConditionOrderState> {
  DerivativeValidateConditionOrderCubit()
    : super(const DerivativeValidateConditionOrderState());
  StockInfoModel? _stockInfo;

  void updateParam({StockInfoModel? stockInfo, OrderType? orderType}) {
    if (stockInfo != null) {
      _stockInfo = stockInfo;
    }
  }

  void validateError({ErrorStepPrice? errorStepPrice, ErrorRange? errorRange}) {
    emit(
      state.copyWith(errorStepPrice: errorStepPrice, errorRange: errorRange),
    );
  }

  void clear() {
    emit(const DerivativeValidateConditionOrderState());
  }

  void focusField(FocusKeyboard type) {
    emit(state.copyWith(focusKeyboard: type));
  }

  void clearSession() {
    emit(state.clearSession());
  }

  onChangeStepPrice(String value) {
    emit(state.copyWith(currentStepPrice: value));
  }

  stepPriceTap({required String text, bool increase = true}) {
    String onTap({required String text, bool increase = true}) {
      const step = 0.1;
      final newText =
          ConditionCommandUtil.updateValueDerivative(
            increase,
            text.priceDerivative ?? 0.0,
            (DerivativeAppConfigs().userConfig.priceJump ?? step).toDouble(),
          ).toDouble();
      if (newText < 0) return 0.0.toFormatThousandSeparator(isReturnZero: true);
      return newText.toFormatThousandSeparator();
    }

    final newText = onTap(text: text, increase: increase);
    onChangeStepPrice(newText);
  }

  onChangeRange(String value) {
    final error = validateRange(text: value);
    emit(state.copyWith(currentRange: value, errorRange: error));
  }

  ErrorRange validateRange({required String text}) {
    if (_stockInfo == null) return ErrorRange.none;

    if (text.isEmpty) return ErrorRange.empty;

    return ErrorRange.none;
  }

  rangeTap({required String text, bool increase = true}) {
    String onTap({required String text, bool increase = true}) {
      const step = 0.1;
      final newText =
          ConditionCommandUtil.updateValueDerivative(
            increase,
            text.priceDerivative ?? 0.0,
            (DerivativeAppConfigs().userConfig.priceJump ?? step).toDouble(),
          ).toDouble();
      if (newText < 0) return 0.0.toFormatThousandSeparator();
      return newText.toFormatThousandSeparator();
    }

    final newText = onTap(text: text, increase: increase);
    onChangeRange(newText);
  }

  priceTap({
    required String text,
    bool increase = true,
    bool stopLoss = false,
    bool conditionStopLoss = false,
  }) {
    if (text.isEmpty) {
      onChangePrice(
        stopLoss: stopLoss,
        conditionStopLoss: conditionStopLoss,
        (_stockInfo?.closePrice ?? _stockInfo?.refPrice ?? 0)
            .toFormatThousandSeparator(),
      );
      return;
    }
    if (text.priceDerivative == 0.0 && !increase) {
      onChangePrice(
        stopLoss: stopLoss,
        conditionStopLoss: conditionStopLoss,
        0.0.toFormatThousandSeparator(),
      );
      return;
    }
    final newText =
        ConditionCommandUtil.updateValueDerivative(
          increase,
          text.priceDerivative ?? 0.0,
          _stepPrice(text),
        ).toDouble().toFormatThousandSeparator();
    final finalText = newText.length > 8 ? text : newText;
    onChangePrice(
      stopLoss: stopLoss,
      conditionStopLoss: conditionStopLoss,
      finalText,
    );
  }

  double _stepPrice(String text) {
    return _stockInfo?.stockType == StockType.FUGB
        ? 1.0
        : _stockInfo?.stockType == StockType.FUVN30 ||
            _stockInfo?.stockType == StockType.FUVN100
        ? (DerivativeAppConfigs().userConfig.priceJump ?? 0.1).toDouble()
        : 0.1;
  }

  onChangePrice(
    String value, {
    bool stopLoss = false,
    bool conditionStopLoss = false,
  }) {
    var currentPrice =
        value.isEmpty
            ? value
            : num.parse(
              value.priceDerivative.toString(),
            ).toFormatThousandSeparator();

    final error = validatePrice(text: value);
    if (stopLoss) {
      emit(state.copyWith(errorStopLoss: error, currentStopLoss: currentPrice));
      return;
    }
    if (conditionStopLoss) {
      emit(
        state.copyWith(
          errorConditionStopLoss: error,
          currentConditionStopLoss: currentPrice,
        ),
      );
      return;
    }
    emit(
      state.copyWith(errorTakeProfit: error, currentTakeProfit: currentPrice),
    );
  }

  ErrorPrice validatePrice({required String text}) {
    if (_stockInfo == null) return ErrorPrice.none;

    final floor = _stockInfo!.floor ?? 0.0;
    final ceiling = _stockInfo!.ceiling ?? 0.0;
    if (text.isEmpty) {
      return ErrorPrice.empty;
    }
    if (_valuePrice(text) > ceiling) {
      return ErrorPrice.overRange;
    }

    if (_valuePrice(text) < floor) {
      return ErrorPrice.underRange;
    }
    return ErrorPrice.none;
  }

  double _valuePrice(String text) {
    return text.priceDerivative ?? 0.0;
  }
}
