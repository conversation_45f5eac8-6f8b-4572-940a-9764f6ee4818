part of 'derivative_validate_condition_order_cubit.dart';

class DerivativeValidateConditionOrderState extends Equatable {
  final ErrorStepPrice errorStepPrice;
  final ErrorRange errorRange;
  final ErrorPrice errorTakeProfit;
  final ErrorPrice errorStopLoss;
  final ErrorPrice errorConditionStopLoss;
  final FocusKeyboard focusKeyboard;
  final String? currentStepPrice;
  final String? currentRange;
  final String? currentTakeProfit;
  final String? currentStopLoss;
  final String? currentConditionStopLoss;

  const DerivativeValidateConditionOrderState({
    this.errorStepPrice = ErrorStepPrice.init,
    this.errorRange = ErrorRange.init,
    this.errorTakeProfit = ErrorPrice.init,
    this.errorStopLoss = ErrorPrice.init,
    this.errorConditionStopLoss = ErrorPrice.init,
    this.focusKeyboard = FocusKeyboard.none,
    this.currentStepPrice,
    this.currentRange,
    this.currentTakeProfit,
    this.currentStopLoss,
    this.currentConditionStopLoss,
  });

  @override
  List<Object?> get props => [
    errorStepPrice,
    errorTakeProfit,
    errorStopLoss,
    errorConditionStopLoss,
    errorRange,
    focusKeyboard,
    currentStepPrice,
    currentRange,
    currentTakeProfit,
    currentStopLoss,
    currentConditionStopLoss,
  ];

  DerivativeValidateConditionOrderState copyWith({
    ErrorStepPrice? errorStepPrice,
    ErrorPrice? errorTakeProfit,
    ErrorPrice? errorStopLoss,
    ErrorPrice? errorConditionStopLoss,
    ErrorRange? errorRange,
    FocusKeyboard? focusKeyboard,
    String? calculateValue,
    String? currentStepPrice,
    String? currentRange,
    String? currentTakeProfit,
    String? currentStopLoss,
    String? currentConditionStopLoss,
  }) {
    return DerivativeValidateConditionOrderState(
      errorStepPrice: errorStepPrice ?? this.errorStepPrice,
      errorTakeProfit: errorTakeProfit ?? this.errorTakeProfit,
      errorStopLoss: errorStopLoss ?? this.errorStopLoss,
      errorConditionStopLoss:
          errorConditionStopLoss ?? this.errorConditionStopLoss,
      errorRange: errorRange ?? this.errorRange,
      focusKeyboard: focusKeyboard ?? this.focusKeyboard,
      currentStepPrice: currentStepPrice ?? this.currentStepPrice,
      currentRange: currentRange ?? this.currentRange,
      currentTakeProfit: currentTakeProfit ?? this.currentTakeProfit,
      currentStopLoss: currentStopLoss ?? this.currentStopLoss,
      currentConditionStopLoss:
          currentConditionStopLoss ?? this.currentConditionStopLoss,
    );
  }

  DerivativeValidateConditionOrderState clearSession() {
    return DerivativeValidateConditionOrderState(
      errorStepPrice: errorStepPrice,
      errorTakeProfit: errorTakeProfit,
      errorRange: errorRange,
      focusKeyboard: focusKeyboard,
      currentStepPrice: currentStepPrice,
      currentRange: currentRange,
      currentTakeProfit: currentTakeProfit,
    );
  }
}

extension ValidateConditionOrderStateExtension
    on DerivativeValidateConditionOrderState {
  bool get isErrorStepPrice => errorStepPrice != ErrorStepPrice.none;

  bool get isErrorRange => errorRange != ErrorRange.none;

  bool get isErrorTakeProfit => errorTakeProfit != ErrorPrice.none;

  bool get isErrorStopLoss => errorStopLoss != ErrorPrice.none;

  bool get isErrorConditionStopLoss =>
      errorConditionStopLoss != ErrorPrice.none;
}
