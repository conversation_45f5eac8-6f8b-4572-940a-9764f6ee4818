// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_trading/core/constant/constants.dart';
import 'package:vp_trading/core/extension/ext.dart';
import 'package:vp_trading/cubit/place_order/place_order/place_order_cubit.dart';
import 'package:vp_trading/cubit/place_order/stock_info/stock_info_cubit.dart';
import 'package:vp_trading/cubit/place_order/validate_order/validate_order_cubit.dart';
import 'package:vp_trading/generated/assets.gen.dart';
import 'package:vp_trading/generated/l10n.dart';
import 'package:vp_trading/screen/place_order/widgets/bottom/order_button.dart';
import 'package:vp_trading/screen/place_order/widgets/bottom/suggest_field_widget.dart';

class OrderBottomLayout extends StatefulWidget {
  const OrderBottomLayout({super.key});

  @override
  State<OrderBottomLayout> createState() => _OrderBottomLayoutState();
}

class _OrderBottomLayoutState extends State<OrderBottomLayout> {
  late StreamSubscription<bool> keyboardSubscription;
  bool _isKeyboardVisible = false;
  @override
  void initState() {
    super.initState();

    var keyboardVisibilityController = KeyboardVisibilityController();
    _isKeyboardVisible = keyboardVisibilityController.isVisible;

    keyboardSubscription = keyboardVisibilityController.onChange.listen((
      bool visible,
    ) {
      setState(() {
        _isKeyboardVisible = visible;
      });
    });
  }

  @override
  void dispose() {
    keyboardSubscription.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      //  height: 63 + MediaQuery.of(context).padding.bottom,
      margin: EdgeInsets.only(
        bottom:
            MediaQuery.of(context).viewInsets.bottom +
            (!_isKeyboardVisible ? MediaQuery.of(context).padding.bottom : 0),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const DividerWidget(),
          Container(
            height: 56,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child:
                _isKeyboardVisible
                    ? Row(
                      children: const [
                        _CloseKeyboardWidget(),
                        Expanded(child: _ValueLabelWidget(center: true)),
                        OrderButton(),
                      ],
                    )
                    : Row(
                      children: const [
                        Expanded(child: _ValueLabelWidget()),
                        OrderButton(),
                      ],
                    ),
          ),
          BlocBuilder<ValidateOrderCubit, ValidateOrderState>(
            buildWhen:
                (previous, current) =>
                    previous.focusKeyboard != current.focusKeyboard,
            builder: (context, state) {
              if (state.focusKeyboard == FocusKeyboard.price) {
                final bidPrices =
                    context
                        .read<StockInfoCubit>()
                        .bidPrices
                        .where((element) => element != null)
                        .toList();

                return SuggestFieldWidget(suggests: bidPrices);
              }
              return const SizedBox();
            },
          ),
        ],
      ),
    );
  }
}

class _CloseKeyboardWidget extends StatelessWidget {
  const _CloseKeyboardWidget();

  @override
  Widget build(BuildContext context) {
    return Container(
      alignment: Alignment.centerLeft,
      width: 70,
      child: GestureDetector(
        onTap: () {
          FocusManager.instance.primaryFocus?.unfocus();
        },
        child: SvgPicture.asset(
          VpTradingAssets.icons.arrowDrop.path,
          package: 'vp_trading',
          colorFilter: ColorFilter.mode(vpColor.iconBrand, BlendMode.srcIn),
        ),
      ),
    );
  }
}

class _ValueLabelWidget extends StatelessWidget {
  final bool center;

  const _ValueLabelWidget({Key? key, this.center = false}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final newOrder = false;
    return BlocBuilder<PlaceOrderCubit, PlaceOrderState>(
      buildWhen: (previous, current) => previous.action != current.action,
      builder: (context, state) {
        return BlocBuilder<ValidateOrderCubit, ValidateOrderState>(
          buildWhen:
              (previous, current) =>
                  previous.calculateValue != current.calculateValue,
          builder: (context, stateValidate) {
            return Column(
              crossAxisAlignment:
                  center ? CrossAxisAlignment.center : CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  newOrder
                      ? VPTradingLocalize.current.trading_order_value
                      : VPTradingLocalize.current.trading_value,
                  style: vpTextStyle.captionMedium.copyColor(
                    vpColor.textPrimary,
                  ),
                ),
                AutoSizeText(
                  stateValidate.calculateValue.isEmpty
                      ? (stateValidate.calculateValue.price ?? 0).valueText
                      : stateValidate.calculateValue,
                  style: vpTextStyle.subtitle14.copyColor(state.action.color),
                  minFontSize: 2,
                ),
              ],
            );
          },
        );
      },
    );
  }
}
