import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/custom_widget/animation/blink.dart' show Blink;
import 'package:vp_trading/cubit/derivative/validate_condition_order/derivative_validate_condition_order_cubit.dart';
import 'package:vp_trading/screen/place_order/widgets/input_view/custom/input_field_box.dart';
import 'package:vp_trading/screen/place_order/widgets/input_view/custom/input_field_error.dart';
import 'package:vp_trading/utils/text_inputformater.dart';
import 'package:vp_trading/vp_trading.dart';

class RangeTextInputField extends StatefulWidget {
  final TextEditingController rangeController;

  const RangeTextInputField({super.key, required this.rangeController});

  @override
  State<RangeTextInputField> createState() => RangeTextInputFieldState();
}

class RangeTextInputFieldState extends State<RangeTextInputField> {
  final ValueNotifier<bool> _rangeBlink = ValueNotifier(false);
  final _rangeFocusNode = FocusNode();

  void _focusListener() {
    if (_rangeFocusNode.hasFocus) {
      context.read<DerivativeValidateConditionOrderCubit>().focusField(
        FocusKeyboard.range,
      );
    } else {
      context.read<DerivativeValidateConditionOrderCubit>().focusField(
        FocusKeyboard.none,
      );
    }
  }

  @override
  void initState() {
    super.initState();
    _rangeFocusNode.addListener(_focusListener);
  }

  @override
  void dispose() {
    _rangeFocusNode.dispose();
    _rangeBlink.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocListener(
      listeners: [
        BlocListener<
          DerivativeValidateConditionOrderCubit,
          DerivativeValidateConditionOrderState
        >(
          listenWhen:
              (previous, current) =>
                  previous.currentRange != current.currentRange,
          listener: (context, state) {
            if (state.currentRange != null) {
              widget.rangeController.text = state.currentRange!;
              widget.rangeController.selection = TextSelection.fromPosition(
                TextPosition(offset: widget.rangeController.text.length),
              );
            }
          },
        ),
        BlocListener<
          DerivativeValidateConditionOrderCubit,
          DerivativeValidateConditionOrderState
        >(
          listenWhen:
              (previous, current) =>
                  previous.focusKeyboard != current.focusKeyboard,
          listener: (context, state) {
            if (state.focusKeyboard == FocusKeyboard.range) {
              _rangeFocusNode.requestFocus();
            }
          },
        ),
      ],
      child: BlocBuilder<
        DerivativeValidateConditionOrderCubit,
        DerivativeValidateConditionOrderState
      >(
        builder: (context, state) {
          return Column(
            children: [
              Row(
                children: [
                  Expanded(
                    flex: 1,
                    child: Text(
                      VPTradingLocalize.current.derivative_range,
                      style: vpTextStyle.body14.copyColor(vpColor.textPrimary),
                    ),
                  ),
                  Expanded(
                    flex: 3,
                    child: ColoredBox(
                      color: vpColor.backgroundElevation0,
                      child: Blink(
                        blink: _rangeBlink,
                        child: InputFieldBox(
                          isError:
                              state.errorRange.isError &&
                              widget.rangeController.text.isNotEmpty,

                          controller: widget.rangeController,
                          maxLength: 10,
                          hintText: 'Chênh lệch',
                          onChange: (value) {
                            context
                                .read<DerivativeValidateConditionOrderCubit>()
                                .onChangeRange(value);
                          },
                          focusNode: _rangeFocusNode,
                          scrollPadding: 180,
                          onTap: (increase) {
                            context
                                .read<DerivativeValidateConditionOrderCubit>()
                                .rangeTap(
                                  text: widget.rangeController.text,
                                  increase: increase,
                                );
                          },
                          inputFormatters: [
                            removeZeroStartInputFormatter,
                            ...volumeInputFormatter,
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              BlocBuilder<
                DerivativeValidateConditionOrderCubit,
                DerivativeValidateConditionOrderState
              >(
                buildWhen:
                    (previous, current) =>
                        previous.errorRange != current.errorRange ||
                        previous.currentRange != current.currentRange,
                builder: (context, state) {
                  return InputFieldError(
                    errorMessage: state.errorRange.message,
                    text: widget.rangeController.text,
                    isShake: true,
                  );
                },
              ),
            ],
          );
        },
      ),
    );
  }
}
