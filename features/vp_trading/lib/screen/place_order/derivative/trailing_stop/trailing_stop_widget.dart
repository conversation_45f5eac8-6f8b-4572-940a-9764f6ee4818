import 'package:flutter/widgets.dart';
import 'package:vp_trading/screen/place_order/derivative/trailing_stop/widget/range_text_input_field.dart';
import 'package:vp_trading/screen/place_order/derivative/trailing_stop/widget/step_price_text_input_field.dart';

class TrailingStopWidget extends StatefulWidget {
  const TrailingStopWidget({super.key});

  @override
  State<TrailingStopWidget> createState() => _TrailingStopWidgetState();
}

class _TrailingStopWidgetState extends State<TrailingStopWidget> {
  final _stepPriceController = TextEditingController();
  final _rangeController = TextEditingController();

  @override
  dispose() {
    _stepPriceController.dispose();
    _rangeController.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        StepPriceTextInputField(stepPriceController: _stepPriceController),
        const Si<PERSON><PERSON><PERSON>(height: 12),
        RangeTextInputField(rangeController: _rangeController),
      ],
    );
  }
}
