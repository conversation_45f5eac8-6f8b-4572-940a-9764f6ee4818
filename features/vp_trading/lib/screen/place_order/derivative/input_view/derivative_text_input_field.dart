import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/custom_widget/animation/blink.dart' show Blink;
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_trading/cubit/derivative/validate_order/derivative_validate_order_cubit.dart';
import 'package:vp_trading/cubit/place_order/available_trade/available_trade_cubit.dart';
import 'package:vp_trading/cubit/place_order/place_order/place_order_cubit.dart';
import 'package:vp_trading/cubit/place_order/stock_info/stock_info_cubit.dart';
import 'package:vp_trading/screen/place_order/widgets/input_view/custom/input_field_box.dart';
import 'package:vp_trading/utils/text_inputformater.dart';
import 'package:vp_trading/vp_trading.dart';

class DerivativeTextInputField extends StatefulWidget {
  final TextEditingController priceController;
  final TextEditingController volumeController;
  final bool isShowPrice;

  const DerivativeTextInputField({
    super.key,
    required this.priceController,
    required this.volumeController,
    this.isShowPrice = true,
  });

  @override
  State<DerivativeTextInputField> createState() =>
      DerivativeTextInputFieldState();
}

class DerivativeTextInputFieldState extends State<DerivativeTextInputField> {
  late final StreamSubscription<String> _subscriptionPrice;
  late final StreamSubscription<String> _subscriptionSocketPrice;

  final ValueNotifier<bool> _priceBlink = ValueNotifier(false);
  final ValueNotifier<bool> _volumeBlink = ValueNotifier(false);
  final _priceFocusNode = FocusNode();
  final _volumeFocusNode = FocusNode();
  late Throttle _availableTradeThrottle;

  void _focusListener() {
    if (_priceFocusNode.hasFocus) {
      context.read<StockInfoCubit>().onRealtimeChanged(false);
      context.read<DerivativeValidateOrderCubit>().focusField(
        FocusKeyboard.price,
      );
    } else if (_volumeFocusNode.hasFocus) {
      context.read<DerivativeValidateOrderCubit>().focusField(
        FocusKeyboard.volume,
      );
    } else {
      context.read<DerivativeValidateOrderCubit>().focusField(
        FocusKeyboard.none,
      );
    }
  }

  @override
  void initState() {
    super.initState();
    _availableTradeThrottle = Throttle(const Duration(milliseconds: 500));
    _priceFocusNode.addListener(_focusListener);
    _volumeFocusNode.addListener(_focusListener);
    if (widget.priceController.text.isNotEmpty) {
      context.read<DerivativeValidateOrderCubit>().onChangePrice(
        widget.priceController.text,
      );
    }
    _subscriptionPrice = context.read<PlaceOrderCubit>().priceStream.listen((
      data,
    ) {
      if (!mounted) return;

      if (context.read<DerivativeValidateOrderCubit>().state.sessionType ==
          null) {
        context.read<DerivativeValidateOrderCubit>().onChangePrice(data);
      } else {
        context.read<DerivativeValidateOrderCubit>().clearSession();
        context.read<DerivativeValidateOrderCubit>().onChangePrice(data);
      }
      if (data.isEmpty) {
        context.read<DerivativeValidateOrderCubit>().onChangePrice("");
        context.read<DerivativeValidateOrderCubit>().onChangeVolume("");
      } else {
        context.read<StockInfoCubit>().onRealtimeChanged(false);
      }
    });
    _subscriptionSocketPrice = context
        .read<StockInfoCubit>()
        .priceStream
        .listen((data) {
          if (!mounted) return;
          if (context.read<DerivativeValidateOrderCubit>().state.sessionType ==
              null) {
            context.read<DerivativeValidateOrderCubit>().onChangePrice(data);
          } else {
            context.read<DerivativeValidateOrderCubit>().clearSession();
            context.read<DerivativeValidateOrderCubit>().onChangePrice(data);
          }
        });
  }

  @override
  void dispose() {
    _availableTradeThrottle.dispose();

    _priceFocusNode.dispose();
    _volumeFocusNode.dispose();
    _priceBlink.dispose();
    _volumeBlink.dispose();
    _subscriptionPrice.cancel();
    _subscriptionSocketPrice.cancel();
    super.dispose();
  }

  void onChangeAvailableTrade() {
    _availableTradeThrottle(() {
      context.read<AvailableTradeCubit>().getAvailableTrade(
        accountId:
            context
                .read<PlaceOrderCubit>()
                .state
                .subAccountType
                .toSubAccountModel()
                ?.id ??
            "",
        symbol: context.read<PlaceOrderCubit>().state.symbol,
        // quotePrice: widget.priceController.text,
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocListener(
      listeners: [
        BlocListener<
          DerivativeValidateOrderCubit,
          DerivativeValidateOrderState
        >(
          listenWhen:
              (previous, current) =>
                  previous.currentVolume != current.currentVolume,
          listener: (context, state) {
            if (state.currentVolume != null) {
              widget.volumeController.text = state.currentVolume!;
              widget.volumeController.selection = TextSelection.fromPosition(
                TextPosition(offset: widget.volumeController.text.length),
              );
            }
          },
        ),
        BlocListener<
          DerivativeValidateOrderCubit,
          DerivativeValidateOrderState
        >(
          listenWhen:
              (previous, current) =>
                  previous.currentPrice != current.currentPrice,
          listener: (context, state) {
            if (state.currentPrice != null) {
              widget.priceController.text = state.currentPrice!;
              widget.priceController.selection = TextSelection.fromPosition(
                TextPosition(offset: widget.priceController.text.length),
              );
              onChangeAvailableTrade();
            }
          },
        ),
      ],
      child: BlocBuilder<
        DerivativeValidateOrderCubit,
        DerivativeValidateOrderState
      >(
        builder: (context, state) {
          return ClipRRect(
            borderRadius: BorderRadius.circular(4),
            child: ColoredBox(
              color: vpColor.backgroundElevation0,
              child: Row(
                children: [
                  if (widget.isShowPrice) ...[
                    Expanded(
                      child: Blink(
                        blink: _priceBlink,
                        child: InputFieldBox(
                          onTapField: () {
                            if (state.sessionType != null) {
                              context
                                  .read<DerivativeValidateOrderCubit>()
                                  .clearSession();
                              _priceFocusNode.requestFocus();
                            }
                          },
                          isError:
                              state.errorPrice.isError &&
                              widget.priceController.text.isNotEmpty,
                          sessionValue: state.sessionType?.name.toUpperCase(),
                          controller: widget.priceController,
                          hintText: 'Giá',
                          onChange: (value) {
                            context.read<StockInfoCubit>().onRealtimeChanged(
                              false,
                            );
                            context
                                .read<DerivativeValidateOrderCubit>()
                                .onChangePrice(value);
                          },
                          focusNode: _priceFocusNode,
                          onTap: (increase) {
                            context
                                .read<DerivativeValidateOrderCubit>()
                                .priceTap(
                                  text: widget.priceController.text,
                                  increase: increase,
                                );
                          },
                          inputFormatters: [
                            removeZeroStartInputFormatter,
                            LengthLimitingTextInputFormatter(8),
                            ...priceDerivativeInputFormatter,
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                  ],

                  Expanded(
                    child: Blink(
                      blink: _volumeBlink,
                      child: InputFieldBox(
                        isError:
                            state.errorVolume.isError &&
                            widget.volumeController.text.isNotEmpty,

                        controller: widget.volumeController,
                        maxLength: 10,
                        hintText: 'KL',
                        onChange: (value) {
                          context
                              .read<DerivativeValidateOrderCubit>()
                              .onChangeVolume(value);
                        },
                        focusNode: _volumeFocusNode,
                        scrollPadding: 180,
                        onTap: (increase) {
                          context
                              .read<DerivativeValidateOrderCubit>()
                              .volumeTap(
                                text: widget.volumeController.text,
                                increase: increase,
                              );
                        },
                        inputFormatters: [
                          removeZeroStartInputFormatter,
                          ...volumeInputFormatter,
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
