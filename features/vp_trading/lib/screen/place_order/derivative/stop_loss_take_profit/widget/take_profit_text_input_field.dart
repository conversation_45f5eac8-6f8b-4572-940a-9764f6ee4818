import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/custom_widget/animation/blink.dart' show Blink;
import 'package:vp_trading/cubit/derivative/validate_condition_order/derivative_validate_condition_order_cubit.dart';
import 'package:vp_trading/cubit/derivative/validate_order/derivative_validate_order_cubit.dart';
import 'package:vp_trading/screen/place_order/widgets/input_view/custom/input_field_box.dart';
import 'package:vp_trading/screen/place_order/widgets/input_view/custom/input_field_error.dart';
import 'package:vp_trading/utils/text_inputformater.dart';
import 'package:vp_trading/vp_trading.dart';

class TakeProfitTextInputField extends StatefulWidget {
  final TextEditingController takeProfitController;

  const TakeProfitTextInputField({
    super.key,
    required this.takeProfitController,
  });

  @override
  State<TakeProfitTextInputField> createState() =>
      TakeProfitTextInputFieldState();
}

class TakeProfitTextInputFieldState extends State<TakeProfitTextInputField> {
  final ValueNotifier<bool> _takeProfitBlink = ValueNotifier(false);
  final _takeProfitFocusNode = FocusNode();

  void _focusListener() {
    if (_takeProfitFocusNode.hasFocus) {
      context.read<DerivativeValidateConditionOrderCubit>().focusField(
        FocusKeyboard.volume,
      );
    } else {
      context.read<DerivativeValidateConditionOrderCubit>().focusField(
        FocusKeyboard.none,
      );
    }
  }

  @override
  void initState() {
    super.initState();
    _takeProfitFocusNode.addListener(_focusListener);
  }

  @override
  void dispose() {
    _takeProfitFocusNode.dispose();
    _takeProfitBlink.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocListener(
      listeners: [
        BlocListener<
          DerivativeValidateConditionOrderCubit,
          DerivativeValidateConditionOrderState
        >(
          listenWhen:
              (previous, current) =>
                  previous.currentTakeProfit != current.currentTakeProfit,
          listener: (context, state) {
            if (state.currentTakeProfit != null) {
              widget.takeProfitController.text = state.currentTakeProfit!;
              widget
                  .takeProfitController
                  .selection = TextSelection.fromPosition(
                TextPosition(offset: widget.takeProfitController.text.length),
              );
            }
          },
        ),
        BlocListener<
          DerivativeValidateConditionOrderCubit,
          DerivativeValidateConditionOrderState
        >(
          listenWhen:
              (previous, current) =>
                  previous.focusKeyboard != current.focusKeyboard,
          listener: (context, state) {
            if (state.focusKeyboard == FocusKeyboard.takeProfit) {
              _takeProfitFocusNode.requestFocus();
            }
          },
        ),
      ],
      child: BlocBuilder<
        DerivativeValidateConditionOrderCubit,
        DerivativeValidateConditionOrderState
      >(
        builder: (context, state) {
          return Column(
            children: [
              Row(
                children: [
                  Expanded(
                    flex: 1,
                    child: Text(
                      VPTradingLocalize.current.trading_take_profit,
                      style: vpTextStyle.body14.copyColor(vpColor.textPrimary),
                    ),
                  ),
                  Expanded(
                    flex: 3,
                    child: ColoredBox(
                      color: vpColor.backgroundElevation0,
                      child: Blink(
                        blink: _takeProfitBlink,
                        child: InputFieldBox(
                          isError:
                              state.errorTakeProfit.isError &&
                              widget.takeProfitController.text.isNotEmpty,

                          controller: widget.takeProfitController,
                          maxLength: 10,
                          hintText: 'Giá',
                          onChange: (value) {
                            context
                                .read<DerivativeValidateConditionOrderCubit>()
                                .onChangePrice(value);
                          },
                          focusNode: _takeProfitFocusNode,
                          scrollPadding: 180,
                          onTap: (increase) {
                            context
                                .read<DerivativeValidateConditionOrderCubit>()
                                .priceTap(
                                  text: widget.takeProfitController.text,
                                  increase: increase,
                                );
                          },
                          inputFormatters: [
                            removeZeroStartInputFormatter,
                            ...volumeInputFormatter,
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              BlocBuilder<
                DerivativeValidateConditionOrderCubit,
                DerivativeValidateConditionOrderState
              >(
                buildWhen:
                    (previous, current) =>
                        previous.errorTakeProfit != current.errorTakeProfit ||
                        previous.currentTakeProfit != current.currentTakeProfit,
                builder: (context, state) {
                  return InputFieldError(
                    errorMessage: state.errorTakeProfit.message,
                    text: widget.takeProfitController.text,
                    isShake: true,
                  );
                },
              ),
            ],
          );
        },
      ),
    );
  }
}
