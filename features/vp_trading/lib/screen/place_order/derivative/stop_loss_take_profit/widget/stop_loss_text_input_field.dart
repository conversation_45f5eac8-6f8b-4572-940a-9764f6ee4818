import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/custom_widget/animation/blink.dart' show Blink;
import 'package:vp_trading/cubit/derivative/validate_condition_order/derivative_validate_condition_order_cubit.dart';
import 'package:vp_trading/cubit/derivative/validate_order/derivative_validate_order_cubit.dart';
import 'package:vp_trading/screen/place_order/widgets/input_view/custom/input_field_box.dart';
import 'package:vp_trading/screen/place_order/widgets/input_view/custom/input_field_error.dart';
import 'package:vp_trading/utils/text_inputformater.dart';
import 'package:vp_trading/vp_trading.dart';

class StopLossTextInputField extends StatefulWidget {
  final TextEditingController stopLossController;

  const StopLossTextInputField({super.key, required this.stopLossController});

  @override
  State<StopLossTextInputField> createState() => StopLossTextInputFieldState();
}

class StopLossTextInputFieldState extends State<StopLossTextInputField> {
  final ValueNotifier<bool> _stopLossBlink = ValueNotifier(false);
  final _stopLossFocusNode = FocusNode();

  void _focusListener() {
    if (_stopLossFocusNode.hasFocus) {
      context.read<DerivativeValidateConditionOrderCubit>().focusField(
        FocusKeyboard.stopLoss,
      );
    } else {
      context.read<DerivativeValidateConditionOrderCubit>().focusField(
        FocusKeyboard.none,
      );
    }
  }

  @override
  void initState() {
    super.initState();
    _stopLossFocusNode.addListener(_focusListener);
  }

  @override
  void dispose() {
    _stopLossFocusNode.dispose();
    _stopLossBlink.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocListener(
      listeners: [
        BlocListener<
          DerivativeValidateConditionOrderCubit,
          DerivativeValidateConditionOrderState
        >(
          listenWhen:
              (previous, current) =>
                  previous.currentStopLoss != current.currentStopLoss,
          listener: (context, state) {
            if (state.currentStopLoss != null) {
              widget.stopLossController.text = state.currentStopLoss!;
              widget.stopLossController.selection = TextSelection.fromPosition(
                TextPosition(offset: widget.stopLossController.text.length),
              );
            }
          },
        ),
        BlocListener<
          DerivativeValidateConditionOrderCubit,
          DerivativeValidateConditionOrderState
        >(
          listenWhen:
              (previous, current) =>
                  previous.focusKeyboard != current.focusKeyboard,
          listener: (context, state) {
            if (state.focusKeyboard == FocusKeyboard.stopLoss) {
              _stopLossFocusNode.requestFocus();
            }
          },
        ),
      ],
      child: BlocBuilder<
        DerivativeValidateConditionOrderCubit,
        DerivativeValidateConditionOrderState
      >(
        builder: (context, state) {
          return Column(
            children: [
              Row(
                children: [
                  Expanded(
                    flex: 1,
                    child: Text(
                      VPTradingLocalize.current.trading_stop_loss,
                      style: vpTextStyle.body14.copyColor(vpColor.textPrimary),
                    ),
                  ),
                  Expanded(
                    flex: 3,
                    child: ColoredBox(
                      color: vpColor.backgroundElevation0,
                      child: Blink(
                        blink: _stopLossBlink,
                        child: InputFieldBox(
                          isError:
                              state.errorStopLoss.isError &&
                              widget.stopLossController.text.isNotEmpty,

                          controller: widget.stopLossController,
                          maxLength: 10,
                          hintText: 'Giá',
                          onChange: (value) {
                            context
                                .read<DerivativeValidateConditionOrderCubit>()
                                .onChangePrice(value, stopLoss: true);
                          },
                          focusNode: _stopLossFocusNode,
                          scrollPadding: 180,
                          onTap: (increase) {
                            context
                                .read<DerivativeValidateConditionOrderCubit>()
                                .priceTap(
                                  text: widget.stopLossController.text,
                                  increase: increase,
                                  stopLoss: true,
                                );
                          },
                          inputFormatters: [
                            removeZeroStartInputFormatter,
                            ...volumeInputFormatter,
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              BlocBuilder<
                DerivativeValidateConditionOrderCubit,
                DerivativeValidateConditionOrderState
              >(
                buildWhen:
                    (previous, current) =>
                        previous.errorStopLoss != current.errorStopLoss ||
                        previous.currentStopLoss != current.currentStopLoss,
                builder: (context, state) {
                  return InputFieldError(
                    errorMessage: state.errorStopLoss.message,
                    text: widget.stopLossController.text,
                    isShake: true,
                  );
                },
              ),
            ],
          );
        },
      ),
    );
  }
}
