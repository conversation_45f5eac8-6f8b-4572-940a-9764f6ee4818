import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/custom_widget/animation/blink.dart' show Blink;
import 'package:vp_trading/cubit/derivative/validate_condition_order/derivative_validate_condition_order_cubit.dart';
import 'package:vp_trading/cubit/derivative/validate_order/derivative_validate_order_cubit.dart';
import 'package:vp_trading/screen/place_order/widgets/input_view/custom/input_field_box.dart';
import 'package:vp_trading/screen/place_order/widgets/input_view/custom/input_field_error.dart';
import 'package:vp_trading/utils/text_inputformater.dart';
import 'package:vp_trading/vp_trading.dart';

class ConditionStopLossTextInputField extends StatefulWidget {
  final TextEditingController conditionStopLossController;

  const ConditionStopLossTextInputField({
    super.key,
    required this.conditionStopLossController,
  });

  @override
  State<ConditionStopLossTextInputField> createState() =>
      ConditionStopLossTextInputFieldState();
}

class ConditionStopLossTextInputFieldState
    extends State<ConditionStopLossTextInputField> {
  final ValueNotifier<bool> _conditionStopLossBlink = ValueNotifier(false);
  final _conditionStopLossFocusNode = FocusNode();

  void _focusListener() {
    if (_conditionStopLossFocusNode.hasFocus) {
      context.read<DerivativeValidateConditionOrderCubit>().focusField(
        FocusKeyboard.conditionStopLoss,
      );
    } else {
      context.read<DerivativeValidateConditionOrderCubit>().focusField(
        FocusKeyboard.none,
      );
    }
  }

  @override
  void initState() {
    super.initState();
    _conditionStopLossFocusNode.addListener(_focusListener);
  }

  @override
  void dispose() {
    _conditionStopLossFocusNode.dispose();
    _conditionStopLossBlink.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocListener(
      listeners: [
        BlocListener<
          DerivativeValidateConditionOrderCubit,
          DerivativeValidateConditionOrderState
        >(
          listenWhen:
              (previous, current) =>
                  previous.currentConditionStopLoss !=
                  current.currentConditionStopLoss,
          listener: (context, state) {
            if (state.currentConditionStopLoss != null) {
              widget.conditionStopLossController.text =
                  state.currentConditionStopLoss!;
              widget
                  .conditionStopLossController
                  .selection = TextSelection.fromPosition(
                TextPosition(
                  offset: widget.conditionStopLossController.text.length,
                ),
              );
            }
          },
        ),
        BlocListener<
          DerivativeValidateConditionOrderCubit,
          DerivativeValidateConditionOrderState
        >(
          listenWhen:
              (previous, current) =>
                  previous.focusKeyboard != current.focusKeyboard,
          listener: (context, state) {
            if (state.focusKeyboard == FocusKeyboard.conditionStopLoss) {
              _conditionStopLossFocusNode.requestFocus();
            }
          },
        ),
      ],
      child: BlocBuilder<
        DerivativeValidateConditionOrderCubit,
        DerivativeValidateConditionOrderState
      >(
        builder: (context, state) {
          return Column(
            children: [
              Row(
                children: [
                  Expanded(
                    flex: 1,
                    child: Text(
                      VPTradingLocalize.current.trading_condition_stop_loss,
                      style: vpTextStyle.body14.copyColor(vpColor.textPrimary),
                    ),
                  ),
                  Expanded(
                    flex: 3,
                    child: ColoredBox(
                      color: vpColor.backgroundElevation0,
                      child: Blink(
                        blink: _conditionStopLossBlink,
                        child: InputFieldBox(
                          isError:
                              state.errorConditionStopLoss.isError &&
                              widget
                                  .conditionStopLossController
                                  .text
                                  .isNotEmpty,

                          controller: widget.conditionStopLossController,
                          maxLength: 10,
                          hintText: 'Giá',
                          onChange: (value) {
                            context
                                .read<DerivativeValidateConditionOrderCubit>()
                                .onChangePrice(value, conditionStopLoss: true);
                          },
                          focusNode: _conditionStopLossFocusNode,
                          scrollPadding: 180,
                          onTap: (increase) {
                            context
                                .read<DerivativeValidateConditionOrderCubit>()
                                .priceTap(
                                  text: widget.conditionStopLossController.text,
                                  increase: increase,
                                  conditionStopLoss: true,
                                );
                          },
                          inputFormatters: [
                            removeZeroStartInputFormatter,
                            ...volumeInputFormatter,
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              BlocBuilder<
                DerivativeValidateConditionOrderCubit,
                DerivativeValidateConditionOrderState
              >(
                buildWhen:
                    (previous, current) =>
                        previous.errorConditionStopLoss !=
                            current.errorConditionStopLoss ||
                        previous.currentConditionStopLoss !=
                            current.currentConditionStopLoss,
                builder: (context, state) {
                  return InputFieldError(
                    errorMessage: state.errorConditionStopLoss.message,
                    text: widget.conditionStopLossController.text,
                    isShake: true,
                  );
                },
              ),
            ],
          );
        },
      ),
    );
  }
}
