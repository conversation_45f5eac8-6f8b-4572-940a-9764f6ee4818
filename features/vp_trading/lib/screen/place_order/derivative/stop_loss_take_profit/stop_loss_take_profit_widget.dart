import 'package:flutter/widgets.dart';
import 'package:vp_trading/screen/place_order/derivative/stop_loss_take_profit/widget/condition_stop_loss_text_input_field.dart';
import 'package:vp_trading/screen/place_order/derivative/stop_loss_take_profit/widget/stop_loss_text_input_field.dart';
import 'package:vp_trading/screen/place_order/derivative/stop_loss_take_profit/widget/take_profit_text_input_field.dart';

class StopLossTakeProfitWidget extends StatefulWidget {
  const StopLossTakeProfitWidget({super.key});

  @override
  State<StopLossTakeProfitWidget> createState() =>
      _StopLossTakeProfitWidgetState();
}

class _StopLossTakeProfitWidgetState extends State<StopLossTakeProfitWidget> {
  final _stopLossController = TextEditingController();
  final _conditionStopLossController = TextEditingController();
  final _takeProfitController = TextEditingController();

  @override
  dispose() {
    _stopLossController.dispose();
    _takeProfitController.dispose();
    _conditionStopLossController.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        TakeProfitTextInputField(takeProfitController: _takeProfitController),
        const SizedBox(height: 12),
        StopLossTextInputField(stopLossController: _stopLossController),
        const SizedBox(height: 12),
        ConditionStopLossTextInputField(
          conditionStopLossController: _conditionStopLossController,
        ),
      ],
    );
  }
}
