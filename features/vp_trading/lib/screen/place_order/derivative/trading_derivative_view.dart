import 'package:flutter/widgets.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/utils/show_error.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_stock_common/extension/context_extensions.dart';
import 'package:vp_stock_common/vp_stock_common.dart';
import 'package:vp_stock_common/widgets/dialog/no_derivative_account_yet.dart';
import 'package:vp_trading/cubit/derivative/derivative_order/derivative_order_cubit.dart';
import 'package:vp_trading/cubit/derivative/derivative_positions/derivative_positions_cubit.dart';
import 'package:vp_trading/cubit/derivative/derivative_regular_order_component/derivative_regular_order_component_cubit.dart';
import 'package:vp_trading/cubit/derivative/validate_condition_order/derivative_validate_condition_order_cubit.dart';
import 'package:vp_trading/cubit/derivative/validate_order/derivative_validate_order_cubit.dart';
import 'package:vp_trading/cubit/place_order/available_trade/available_trade_cubit.dart';
import 'package:vp_trading/cubit/place_order/order_suggest/order_suggest_cubit.dart';
import 'package:vp_trading/cubit/place_order/place_order/place_order_cubit.dart';
import 'package:vp_trading/cubit/place_order/stock_info/stock_info_cubit.dart';
import 'package:vp_trading/cubit/place_order/submit/place_order_submit_cubit.dart';
import 'package:vp_trading/model/positions/order_contract_code_model.dart';
import 'package:vp_trading/router/trading_router.dart';
import 'package:vp_trading/screen/derivative_utility/drivative_utils.dart';
import 'package:vp_trading/screen/place_order/derivative/derivative_optional_order/derivative_optional_order_component.dart';
import 'package:vp_trading/screen/place_order/derivative/derivative_optional_order/derivative_optional_order_widget.dart';

class TradingDerivativeView extends StatelessWidget {
  TradingDerivativeView({required PlaceOrderArgs? args, super.key})
    : args = args ?? PlaceOrderArgs(orderType: PlaceOrderType.derivative);

  final PlaceOrderArgs args;

  String? get symbol => args.symbol;

  @override
  Widget build(BuildContext context) {
    if (symbol.isNullOrEmpty) {
      return BlocProvider(
        create: (_) => StockInfoCubit()..loadFuQuotes(),
        child: BlocBuilder<StockInfoCubit, StockInfoState>(
          builder: (context, state) {
            return VPStatusBuilder(
              apiStatus: state.status,
              onRetry: () => context.read<StockInfoCubit>().loadFuQuotes(),
              builder: (_, __) {
                final quote = state.findNearestFI();

                if (quote == null) {
                  return const NoDataView();
                }

                return TradingDerivativeBodyView(
                  args: args.copyWith(symbol: quote.symbol),
                );
              },
            );
          },
        ),
      );
    }

    return TradingDerivativeBodyView(args: args);
  }
}

class TradingDerivativeBodyView extends StatefulWidget {
  const TradingDerivativeBodyView({required this.args, super.key});

  final PlaceOrderArgs args;

  @override
  State<TradingDerivativeBodyView> createState() =>
      _TradingDerivativeBodyViewState();
}

class _TradingDerivativeBodyViewState extends State<TradingDerivativeBodyView> {
  late PlaceOrderArgs args = widget.args;

  SubAccountModel? get subAccount =>
      GetIt.instance.get<SubAccountCubit>().derivativeActiveAccount;

  SubAccountType? get subAccountType =>
      args.subAccountType ?? subAccount?.toSubAccountType;

  String get symbol => args.symbol!;

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback(
      (_) => DerivativeUtils.checkDerivativeAccount(context),
    );
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(create: (context) => DerivativeOrderCubit()),
        BlocProvider(
          create:
              (_) => PlaceOrderCubit(
                symbol: symbol,
                action: args.action,
                subAccountType: subAccountType,
              ),
        ),
        BlocProvider(
          create: (context) => DerivativeRegularOrderComponentCubit(),
        ),
        BlocProvider(
          create: (_) => StockInfoCubit()..loadData(symbol, isDerivative: true),
        ),
        BlocProvider(
          create:
              (_) => DerivativeValidateOrderCubit()..getUserConfigFromData(),
        ),
        BlocProvider(create: (_) => DerivativeValidateConditionOrderCubit()),
        BlocProvider(create: (_) => PlaceOrderSubmitCubit()),
        BlocProvider(create: (_) => OrderSuggestCubit()),
        BlocProvider(
          create:
              (_) =>
                  DerivativePositionsCubit()..loadingData(
                    symbol: symbol,
                    accountId: subAccountType?.toSubAccountModel()?.id ?? '',
                  ),
        ),
        BlocProvider(
          create:
              (_) =>
                  AvailableTradeCubit()..getAvailableTrade(
                    accountId: subAccountType?.toSubAccountModel()?.id ?? "",
                    symbol: symbol,
                  ),
        ),
      ],
      child: MultiBlocListener(
        listeners: [
          BlocListener<StockInfoCubit, StockInfoState>(
            listenWhen:
                (previous, current) =>
                    previous.stockInfo == null && current.stockInfo != null,
            listener: (context, state) {
              final derivativeAccount =
                  GetIt.instance<SubAccountCubit>().derivativeActiveAccount;

              if (derivativeAccount == null) {
                showNoDerivativeAccountDialog(
                  context: context,
                  onRegister:
                      () => context.push(
                        TradingRouter.derivativeContract.routeName,
                      ),
                );
              }

              context.read<DerivativeValidateOrderCubit>().updateParam(
                action: args.action,
              );

              Future.delayed(const Duration(milliseconds: 200), () {
                if (context.mounted) {
                  context.read<StockInfoCubit>().onRealtimeChanged(true);
                }
              });
            },
          ),
          BlocListener<StockInfoCubit, StockInfoState>(
            listenWhen:
                (previous, current) => previous.stockInfo != current.stockInfo,
            listener: (context, state) {
              //when choose buy or sell
              context.read<DerivativeValidateOrderCubit>().updateParam(
                stockInfo: state.stockInfo,
              );
              context.read<DerivativeValidateConditionOrderCubit>().updateParam(
                stockInfo: state.stockInfo,
              );
              final newContractCode = OrderContractCodeModel(
                contractCode: state.stockInfo?.symbol ?? '',
              );
              context.read<DerivativePositionsCubit>().updateParam(
                stockInfo: state.stockInfo,
                contractCode: newContractCode,
              );
              context.read<DerivativeValidateOrderCubit>().clear();
              context.read<OrderSuggestCubit>().stockInfo = state.stockInfo;
              context.read<OrderSuggestCubit>().getMarketStatus();
            },
          ),
          BlocListener<PlaceOrderCubit, PlaceOrderState>(
            listenWhen:
                (previous, current) =>
                    previous.action != current.action ||
                    previous.orderType != current.orderType,
            listener: (context, state) {
              context.read<DerivativeValidateOrderCubit>().updateParam(
                action: state.action,
                orderType: state.orderType,
              );
            },
          ),

          BlocListener<PlaceOrderCubit, PlaceOrderState>(
            listenWhen:
                (previous, current) => previous.orderType != current.orderType,
            listener: (context, state) {
              context.read<OrderSuggestCubit>().orderType = state.orderType;
              context.read<OrderSuggestCubit>().updateSuggest();
            },
          ),

          BlocListener<PlaceOrderCubit, PlaceOrderState>(
            listenWhen:
                (previous, current) =>
                    previous.subAccountType != current.subAccountType,
            listener: (context, state) {
              context.read<AvailableTradeCubit>().getAvailableTrade(
                accountId: state.subAccountType.toSubAccountModel()?.id ?? "",
                symbol: state.symbol,
              );
              context.read<StockInfoCubit>().onOrderLotTypeChanged(
                state.orderLotType,
              );
              context.read<StockInfoCubit>().loadData(
                state.symbol,
                isDerivative: true,
              );
            },
          ),

          BlocListener<PlaceOrderCubit, PlaceOrderState>(
            listenWhen:
                (previous, current) =>
                    previous.symbol != current.symbol ||
                    previous.orderLotType != current.orderLotType,
            listener: (context, state) {
              context.read<AvailableTradeCubit>().getAvailableTrade(
                accountId: state.subAccountType.toSubAccountModel()?.id ?? "",
                symbol: state.symbol,
              );
              context.read<StockInfoCubit>().onOrderLotTypeChanged(
                state.orderLotType,
              );
              context.read<StockInfoCubit>().loadData(
                state.symbol,
                isDerivative: true,
              );
              context.read<DerivativePositionsCubit>().loadingData(
                symbol: state.symbol,
                accountId: state.subAccountType.toSubAccountModel()?.id ?? "",
              );
              context.read<DerivativeValidateOrderCubit>().clear();
              context.read<PlaceOrderCubit>().clear();
            },
          ),
          BlocListener<AvailableTradeCubit, AvailableTradeState>(
            listenWhen:
                (previous, current) =>
                    previous.availableTrade != current.availableTrade,
            listener: (context, state) {
              context.read<DerivativeValidateOrderCubit>().updateParam(
                availableTrade: state.availableTrade,
              );
            },
          ),
          BlocListener<PlaceOrderSubmitCubit, PlaceOrderSubmitState>(
            listener: (context, state) {
              if (state.status.isSuccess) {
                context.read<AvailableTradeCubit>().getAvailableTrade(
                  accountId:
                      context
                          .read<PlaceOrderCubit>()
                          .state
                          .subAccountType
                          .toSubAccountModel()
                          ?.id ??
                      "",
                  symbol: context.read<PlaceOrderCubit>().state.symbol,
                );
                context.showSuccess(content: "Yêu cầu đặt lệnh thành công");
                if (!context.read<PlaceOrderCubit>().state.isSaveCommand) {
                  context.read<DerivativeValidateOrderCubit>().clear();
                }
              }
            },
          ),
          BlocListener<StockInfoCubit, StockInfoState>(
            listenWhen:
                (pState, state) =>
                    pState.status != state.status && state.status.isError,
            listener: (context, state) {
              showError(state.status.error);
            },
          ),
        ],
        child: BlocBuilder<StockInfoCubit, StockInfoState>(
          buildWhen: (pState, state) => pState.status != state.status,
          builder: (context, state) {
            return VPLoadingBuilder(
              showLoading: state.status.isLoading,
              builder: (context, child) {
                return const Column(
                  children: [
                    Expanded(
                      child: SingleChildScrollView(
                        child: DerivativeOptionalOrderWidget(),
                      ),
                    ),

                    DerivativeOptionalOrderComponent(),
                  ],
                );
              },
            );
          },
        ),
      ),
    );
  }
}
