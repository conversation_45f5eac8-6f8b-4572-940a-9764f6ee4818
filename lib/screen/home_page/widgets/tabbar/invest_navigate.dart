import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:new_neo_invest/gen/assets.gen.dart' as bond;
import 'package:new_neo_invest/screen/check_current_status_wealth/check_current_status_wealth_mixin.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/cubit/auth_cubit.dart';
import 'package:vp_core/firebase/remote_config/remote_config_service.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_finvest/generated/assets.gen.dart';
import 'package:vp_fund/generated/assets.gen.dart';
import 'package:vp_fund/generated/l10n.dart';
import 'package:vp_portfolio/generated/assets.gen.dart';
import 'package:vp_wealth/generated/assets.gen.dart' as wealth;

class InvestNavigate extends StatelessWidget with CheckCurrentStatusWealth {
  const InvestNavigate({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      clipBehavior: Clip.hardEdge,
      padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
      decoration: BoxDecoration(
        color: themeData.bgPopup,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(12),
          topRight: Radius.circular(12),
        ),
      ),
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 16),
            Center(
              child: Container(
                width: 56,
                height: 8,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  color: themeData.buttonTopBottomSheet,
                ),
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'Đầu tư',
              style: vpTextStyle.subtitle16.copyColor(vpColor.textPrimary),
            ),
            BodyInvestNavigate(),
            const SizedBox(height: 24),
          ],
        ),
      ),
    );
  }
}

class ItemInvestNavigate extends StatelessWidget {
  const ItemInvestNavigate({
    required this.image,
    required this.text,
    this.package,
    super.key,
    this.onTap,
  });

  final String image;
  final String? package;
  final String text;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: InkWell(
        onTap: onTap,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            AppIconBg(
              icon: SvgPicture.asset(
                image,
                package: package,
              ),
              size: 56,
              padding: 14,
            ),
            const SizedBox(height: 8),
            Text(
              text,
              style: vpTextStyle.subtitle14.copyColor(themeData.black),
            ),
          ],
        ),
      ),
    );
  }
}

class BodyInvestNavigate extends StatelessWidget with CheckCurrentStatusWealth {
  BodyInvestNavigate({super.key});

  bool _hasWealth = false;

  Future<void> checkWealth() async {
    final showWealth = await RemoteConfigService().getShowWealth();
    if (showWealth) {
      final iamInfo = GetIt.instance<AuthCubit>().customerInfoIam;
      _hasWealth = iamInfo?.type == 'I' && iamInfo?.investorType == '001';
    }
  }

  @override
  Widget build(BuildContext context) {
    checkWealth();
    final body = <Widget>[
      ItemInvestNavigate(
        image: bond.Assets.icons.icBond,
        text: 'Trái phiếu',
        onTap: () => context
          ..pop()
          ..push('/bondManagerPage'),
      ),
      ItemInvestNavigate(
        image: VpPortfolioAssets.icons.icEPortfolio.path,
        package: VpPortfolioAssets.package,
        text: 'ePortfolio',
        onTap: () => context
          ..pop()
          ..push('/dashBoard'),
      ),
      ItemInvestNavigate(
        image: VpFundAssets.icons.icFund.path,
        package: VpFundAssets.package,
        text: VPFundLocalize.current.efund,
        onTap: () => context
          ..pop()
          ..push('/fundMainPage'),
      ),
      ItemInvestNavigate(
        image: VpFinvestAssets.icons.icFinvest.path,
        package: VpFinvestAssets.package,
        text: 'Finvest',
        onTap: () => context
          ..pop()
          ..push('/finvest'),
      ),
      if (_hasWealth)
        ItemInvestNavigate(
          image: wealth.Assets.icons.icWealth.path,
          package: wealth.Assets.package,
          text: 'Tích sản',
          onTap: () {
            checkCurrentStatusWealth(context);
          },
        )
    ];
    return GridView.builder(
      padding: const EdgeInsets.only(top: 16),
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: body.length,
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        mainAxisSpacing: 4,
        crossAxisSpacing: 8,
        childAspectRatio: 1.5,
      ),
      itemBuilder: (context, i) {
        return body[i];
      },
    );
  }
}
