enum MarketStatus {
  /// Trước phiên
  preOpen("PO"),

  /// Phiên Buy In
  buyIn("B"),

  /// Phiên ATO
  ato("ATO"),

  /// Phiên liên tục
  continuous("LO"),

  /// Phiên nghỉ trưa
  lunchBreak("L"),

  /// Phiên ATC
  atc("ATC"),

  /// Phiên thỏa thuận
  putThrough("PT"),

  /// Phiên sau giờ (PLO)
  plo("PLO"),

  /// <PERSON><PERSON>g cửa
  close("C"),

  /// Kết thúc nghỉ giữa khớp lệnh liên tục
  endBreakContinuous("F"),

  /// Ngừng giao dịch
  halt("H"),

  /// Bắt đầu EOD
  startEod("G"),

  /// Kết thúc EOD
  endEod("J"),

  /// Trước giờ
  preTime("PRE");

  final String value;

  const MarketStatus(this.value);
}
